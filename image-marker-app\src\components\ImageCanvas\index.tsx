import React, { useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { Card, message, Spin } from 'antd';
import { useApp } from '../../context/AppContext';
import { MarkerPoint } from '../../types';
import { generateId } from '../../utils';
import './index.css';

const ImageCanvas: React.FC = () => {
  const { state, addMarker, updateMarker, selectMarkers } = useApp();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const [loading, setLoading] = useState(false);
  const markersRef = useRef<Map<string, fabric.Circle>>(new Map());

  // 初始化画布
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = new fabric.Canvas(canvasRef.current, {
      selection: true,
      preserveObjectStacking: true,
      backgroundColor: '#f5f5f5'
    });

    fabricCanvasRef.current = canvas;

    // 画布事件监听
    canvas.on('mouse:down', handleCanvasClick);
    canvas.on('selection:created', handleSelectionCreated);
    canvas.on('selection:updated', handleSelectionUpdated);
    canvas.on('selection:cleared', handleSelectionCleared);
    canvas.on('object:moved', handleObjectMoved);

    return () => {
      canvas.dispose();
    };
  }, []);

  // 加载图片到画布
  useEffect(() => {
    if (!state.currentImage || !fabricCanvasRef.current) return;

    setLoading(true);
    const canvas = fabricCanvasRef.current;

    fabric.Image.fromURL(state.currentImage.url, (img) => {
      // 清空画布
      canvas.clear();
      markersRef.current.clear();

      // 计算画布尺寸和缩放比例
      const containerWidth = canvas.getElement().parentElement?.clientWidth || 800;
      const containerHeight = canvas.getElement().parentElement?.clientHeight || 600;
      const maxWidth = containerWidth - 40;
      const maxHeight = containerHeight - 40;

      const scaleX = maxWidth / img.width!;
      const scaleY = maxHeight / img.height!;
      const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

      const scaledWidth = img.width! * scale;
      const scaledHeight = img.height! * scale;

      // 设置画布尺寸
      canvas.setDimensions({
        width: scaledWidth,
        height: scaledHeight
      });

      // 设置图片
      img.set({
        left: 0,
        top: 0,
        scaleX: scale,
        scaleY: scale,
        selectable: false,
        evented: false
      });

      canvas.add(img);
      canvas.sendToBack(img);
      canvas.renderAll();

      setLoading(false);
    }, {
      crossOrigin: 'anonymous'
    });
  }, [state.currentImage]);

  // 渲染标记点
  useEffect(() => {
    if (!fabricCanvasRef.current || !state.currentImage) return;

    const canvas = fabricCanvasRef.current;
    
    // 清除现有标记
    markersRef.current.forEach((marker) => {
      canvas.remove(marker);
    });
    markersRef.current.clear();

    // 添加新标记
    state.markers.forEach((marker) => {
      if (marker.visible) {
        addMarkerToCanvas(marker);
      }
    });

    canvas.renderAll();
  }, [state.markers, state.currentImage]);

  // 添加标记到画布
  const addMarkerToCanvas = (marker: MarkerPoint) => {
    if (!fabricCanvasRef.current) return;

    const canvas = fabricCanvasRef.current;
    const circle = new fabric.Circle({
      left: marker.x - 5,
      top: marker.y - 5,
      radius: 5,
      fill: marker.color,
      stroke: '#ffffff',
      strokeWidth: 2,
      selectable: true,
      hasControls: false,
      hasBorders: false,
      hoverCursor: 'pointer',
      moveCursor: 'move'
    });

    // 添加标记ID到对象
    (circle as any).markerId = marker.id;

    // 添加标记名称标签
    const text = new fabric.Text(marker.name, {
      left: marker.x + 8,
      top: marker.y - 8,
      fontSize: 12,
      fill: marker.color,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      selectable: false,
      evented: false
    });

    const group = new fabric.Group([circle, text], {
      left: marker.x - 5,
      top: marker.y - 5,
      selectable: true,
      hasControls: false,
      hasBorders: true
    });

    (group as any).markerId = marker.id;

    canvas.add(group);
    markersRef.current.set(marker.id, group as any);
  };

  // 处理画布点击
  const handleCanvasClick = (e: fabric.IEvent) => {
    if (!state.currentImage || !fabricCanvasRef.current) return;

    const canvas = fabricCanvasRef.current;
    const pointer = canvas.getPointer(e.e);

    // 检查是否点击在现有对象上
    const target = canvas.findTarget(e.e, false);
    if (target && (target as any).markerId) return;

    // 在点击位置添加新标记
    const selectedGroup = state.groups.find(g => g.id === state.selectedGroup);
    if (!selectedGroup) {
      message.error('请先选择一个标记分组');
      return;
    }

    const markerName = `标记${state.markers.length + 1}`;
    
    addMarker(pointer.x, pointer.y, markerName);
    message.success(`已添加标记：${markerName}`);
  };

  // 处理选择创建
  const handleSelectionCreated = (e: fabric.IEvent) => {
    const activeObjects = fabricCanvasRef.current?.getActiveObjects() || [];
    const selectedIds = activeObjects
      .map(obj => (obj as any).markerId)
      .filter(id => id);
    
    selectMarkers(selectedIds);
  };

  // 处理选择更新
  const handleSelectionUpdated = (e: fabric.IEvent) => {
    const activeObjects = fabricCanvasRef.current?.getActiveObjects() || [];
    const selectedIds = activeObjects
      .map(obj => (obj as any).markerId)
      .filter(id => id);
    
    selectMarkers(selectedIds);
  };

  // 处理选择清除
  const handleSelectionCleared = () => {
    selectMarkers([]);
  };

  // 处理对象移动
  const handleObjectMoved = (e: fabric.IEvent) => {
    const target = e.target;
    if (!target || !(target as any).markerId) return;

    const markerId = (target as any).markerId;
    const newX = target.left! + 5; // 补偿圆心偏移
    const newY = target.top! + 5;

    updateMarker(markerId, { x: newX, y: newY });
  };

  if (!state.currentImage) {
    return (
      <Card className="canvas-placeholder">
        <div className="placeholder-content">
          <p>请先上传图片</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="image-canvas-card">
      <div className="canvas-container">
        <Spin spinning={loading} tip="加载图片中...">
          <canvas ref={canvasRef} className="fabric-canvas" />
        </Spin>
      </div>
      <div className="canvas-instructions">
        <p>• 点击图片任意位置添加标记点</p>
        <p>• 拖拽标记点可以移动位置</p>
        <p>• 点击标记点可以选择，支持多选</p>
      </div>
    </Card>
  );
};

export default ImageCanvas;
