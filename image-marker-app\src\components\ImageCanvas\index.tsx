import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Card, message, Spin } from 'antd';
import { useApp } from '../../context/AppContext';
import type { MarkerPoint } from '../../types';
import './index.css';

// 声明全局fabric对象类型
declare global {
  interface Window {
    fabric: any;
    fabricReady: boolean;
    fabricError: boolean;
  }
}

// 使用全局fabric对象（与调试页面相同的方式）
const getFabric = () => {
  if (typeof window !== 'undefined' && window.fabric) {
    return window.fabric;
  }
  return null;
};

const ImageCanvas: React.FC = () => {
  const { state, addMarker, updateMarker, selectMarkers } = useApp();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [fabricReady, setFabricReady] = useState(false);
  const markersRef = useRef<Map<string, any>>(new Map());

  // 监听Fabric.js加载状态
  useEffect(() => {
    const handleFabricReady = () => {
      console.log('🎉 收到Fabric.js加载完成事件');
      setFabricReady(true);
    };

    const handleFabricError = (event: any) => {
      console.error('❌ 收到Fabric.js加载失败事件:', event.detail);
      message.error('Fabric.js加载失败，请刷新页面重试');
    };

    // 检查是否已经加载
    if (window.fabricReady) {
      setFabricReady(true);
    } else if (window.fabricError) {
      message.error('Fabric.js加载失败，请刷新页面重试');
    } else {
      // 监听加载事件
      window.addEventListener('fabricReady', handleFabricReady);
      window.addEventListener('fabricError', handleFabricError);
    }

    return () => {
      window.removeEventListener('fabricReady', handleFabricReady);
      window.removeEventListener('fabricError', handleFabricError);
    };
  }, []);

  // 初始化画布 - 等待Fabric.js加载完成
  useEffect(() => {
    if (!canvasRef.current || !fabricReady) return;

    const fabric = getFabric();
    if (!fabric) {
      console.error('❌ Fabric.js 未加载，无法初始化Canvas');
      message.error('Fabric.js 未加载，请刷新页面重试');
      return;
    }

    console.log('✅ 开始初始化Canvas，Fabric.js版本:', fabric.version);

    const canvas = new fabric.Canvas(canvasRef.current, {
      selection: true,
      preserveObjectStacking: true,
      backgroundColor: '#f5f5f5'
    });

    fabricCanvasRef.current = canvas;

    // 画布事件监听
    canvas.on('mouse:down', handleCanvasClick);
    canvas.on('selection:created', handleSelectionCreated);
    canvas.on('selection:updated', handleSelectionUpdated);
    canvas.on('selection:cleared', handleSelectionCleared);
    canvas.on('object:moving', handleObjectMoved);

    return () => {
      canvas.dispose();
    };
  }, [fabricReady]);

  // 图片处理函数 - 使用useCallback优化
  const processLoadedImage = useCallback((img: any, canvas: any) => {
    try {
      // 获取图片实际尺寸
      const imgWidth = img.width || state.currentImage?.width || 800;
      const imgHeight = img.height || state.currentImage?.height || 600;

      console.log('处理图片尺寸:', { imgWidth, imgHeight });

      // 计算画布尺寸和缩放比例
      const containerElement = canvas.getElement().parentElement;
      const containerWidth = containerElement?.clientWidth || 800;
      const containerHeight = containerElement?.clientHeight || 600;
      const maxWidth = Math.max(containerWidth - 80, 400); // 确保最小宽度
      const maxHeight = Math.max(containerHeight - 120, 300); // 确保最小高度

      const scaleX = maxWidth / imgWidth;
      const scaleY = maxHeight / imgHeight;
      const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

      const scaledWidth = imgWidth * scale;
      const scaledHeight = imgHeight * scale;

      console.log('缩放计算:', {
        containerSize: `${containerWidth}x${containerHeight}`,
        maxSize: `${maxWidth}x${maxHeight}`,
        scale,
        scaledSize: `${scaledWidth}x${scaledHeight}`
      });

      // 设置画布尺寸
      canvas.setDimensions({
        width: scaledWidth,
        height: scaledHeight
      });

      // 设置图片属性
      img.set({
        left: 0,
        top: 0,
        scaleX: scale,
        scaleY: scale,
        selectable: false,
        evented: false
      });

      // 添加图片到画布
      canvas.add(img);

      // 确保图片在最底层
      canvas.sendObjectToBack(img);

      // 强制重新渲染
      canvas.renderAll();

      console.log('图片加载成功:', {
        originalSize: `${imgWidth}x${imgHeight}`,
        scaledSize: `${scaledWidth}x${scaledHeight}`,
        scale: scale,
        canvasObjects: canvas.getObjects().length
      });

      setLoading(false);
    } catch (error) {
      console.error('图片处理失败:', error);
      message.error('图片处理失败，请重试');
      setLoading(false);
    }
  }, [state.currentImage]);

  // 加载图片到画布
  useEffect(() => {
    if (!state.currentImage || !fabricCanvasRef.current) return;

    setLoading(true);
    const canvas = fabricCanvasRef.current;

    console.log('开始加载图片:', state.currentImage.url);

    const fabric = getFabric();
    if (!fabric) {
      console.error('❌ Fabric.js 未加载');
      message.error('Fabric.js 未加载，请刷新页面重试');
      setLoading(false);
      return;
    }

    // 检查FabricImage.fromURL是否存在
    if (typeof fabric.FabricImage?.fromURL !== 'function') {
      console.error('❌ fabric.FabricImage.fromURL 方法不存在');
      message.error('Fabric.js API不兼容，请检查版本');
      setLoading(false);
      return;
    }

    console.log('✅ 开始使用 fabric.FabricImage.fromURL 加载图片');

    // 使用Fabric.js v6的新API，添加更多调试信息
    fabric.FabricImage.fromURL(state.currentImage.url, {
      crossOrigin: 'anonymous'
    })
      .then((img: any) => {
        try {
          console.log('图片对象创建成功:', {
            img,
            width: img.width,
            height: img.height,
            element: img.getElement?.()
          });

          // 清空画布
          canvas.clear();
          markersRef.current.clear();

          // 等待图片完全加载
          const imgElement = img.getElement?.() as HTMLImageElement;
          if (imgElement && !imgElement.complete) {
            console.log('图片还未完全加载，等待加载完成...');
            imgElement.onload = () => {
              console.log('图片加载完成，重新处理...');
              processLoadedImage(img, canvas);
            };
            imgElement.onerror = (error) => {
              console.error('图片加载出错:', error);
              message.error('图片加载失败');
              setLoading(false);
            };
            return;
          }

          processLoadedImage(img, canvas);
        } catch (error) {
          console.error('图片处理失败:', error);
          message.error('图片处理失败，请重试');
          setLoading(false);
        }
      })
      .catch((error: any) => {
        console.error('图片加载失败:', error);
        message.error('图片加载失败，请检查图片格式或网络连接');
        setLoading(false);
      });
  }, [state.currentImage, processLoadedImage]);

  // 渲染标记点
  useEffect(() => {
    if (!fabricCanvasRef.current || !state.currentImage) return;

    const canvas = fabricCanvasRef.current;
    
    // 清除现有标记
    markersRef.current.forEach((marker) => {
      canvas.remove(marker);
    });
    markersRef.current.clear();

    // 添加新标记
    state.markers.forEach((marker) => {
      if (marker.visible) {
        addMarkerToCanvas(marker);
      }
    });

    canvas.renderAll();
  }, [state.markers, state.currentImage]);

  // 添加标记到画布
  const addMarkerToCanvas = (marker: MarkerPoint) => {
    if (!fabricCanvasRef.current) return;

    const fabric = getFabric();
    if (!fabric) {
      console.error('❌ Fabric.js 未加载，无法添加标记');
      return;
    }

    const canvas = fabricCanvasRef.current;
    const circle = new fabric.Circle({
      left: marker.x - 5,
      top: marker.y - 5,
      radius: 5,
      fill: marker.color,
      stroke: '#ffffff',
      strokeWidth: 2,
      selectable: true,
      hasControls: false,
      hasBorders: false,
      hoverCursor: 'pointer',
      moveCursor: 'move'
    });

    // 添加标记ID到对象
    (circle as any).markerId = marker.id;

    // 添加标记名称标签
    const text = new fabric.Text(marker.name, {
      left: marker.x + 8,
      top: marker.y - 8,
      fontSize: 12,
      fill: marker.color,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      selectable: false,
      evented: false
    });

    const group = new fabric.Group([circle, text], {
      left: marker.x - 5,
      top: marker.y - 5,
      selectable: true,
      hasControls: false,
      hasBorders: true
    });

    (group as any).markerId = marker.id;

    canvas.add(group);
    markersRef.current.set(marker.id, group);
  };

  // 处理画布点击
  const handleCanvasClick = (e: any) => {
    if (!state.currentImage || !fabricCanvasRef.current) return;

    const canvas = fabricCanvasRef.current;
    const pointer = canvas.getPointer(e.e);

    // 检查是否点击在现有对象上
    const target = canvas.findTarget(e.e);
    if (target && (target as any).markerId) return;

    // 在点击位置添加新标记
    const selectedGroup = state.groups.find(g => g.id === state.selectedGroup);
    if (!selectedGroup) {
      message.error('请先在左侧面板选择一个标记分组');
      return;
    }

    const markerName = `标记${state.markers.length + 1}`;

    // 添加标记点
    addMarker(pointer.x, pointer.y, markerName);

    // 显示成功提示
    message.success({
      content: `✅ 已添加标记：${markerName}`,
      duration: 2,
      style: {
        marginTop: '10vh',
      },
    });

    console.log('添加标记:', {
      name: markerName,
      position: `(${Math.round(pointer.x)}, ${Math.round(pointer.y)})`,
      group: selectedGroup.name
    });
  };

  // 处理选择创建
  const handleSelectionCreated = (_e: any) => {
    const activeObjects = fabricCanvasRef.current?.getActiveObjects() || [];
    const selectedIds = activeObjects
      .map((obj: any) => obj.markerId)
      .filter((id: any) => id);

    selectMarkers(selectedIds);
  };

  // 处理选择更新
  const handleSelectionUpdated = (_e: any) => {
    const activeObjects = fabricCanvasRef.current?.getActiveObjects() || [];
    const selectedIds = activeObjects
      .map((obj: any) => obj.markerId)
      .filter((id: any) => id);

    selectMarkers(selectedIds);
  };

  // 处理选择清除
  const handleSelectionCleared = () => {
    selectMarkers([]);
  };

  // 处理对象移动
  const handleObjectMoved = (e: any) => {
    const target = e.target;
    if (!target || !(target as any).markerId) return;

    const markerId = (target as any).markerId;
    const newX = (target.left || 0) + 5; // 补偿圆心偏移
    const newY = (target.top || 0) + 5;

    updateMarker(markerId, { x: newX, y: newY });
  };

  if (!state.currentImage) {
    return (
      <Card className="canvas-placeholder">
        <div className="placeholder-content">
          <div className="placeholder-icon">📷</div>
          <h3>开始标记您的图片</h3>
          <p>请先在左侧面板上传图片文件</p>
          <div className="placeholder-tips">
            <p>💡 支持的格式：JPG、PNG、GIF、WebP</p>
            <p>📏 建议尺寸：不超过10MB</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className="image-canvas-card"
      title={
        <div className="canvas-header">
          <span>图片标记画布</span>
          <div className="canvas-status">
            {state.markers.length > 0 && (
              <span className="marker-count">已添加 {state.markers.length} 个标记</span>
            )}
          </div>
        </div>
      }
    >
      <div className="canvas-container" data-has-image={!!state.currentImage}>
        <Spin spinning={loading} tip="正在加载图片，请稍候...">
          <canvas ref={canvasRef} className="fabric-canvas" />
          {!loading && state.currentImage && state.markers.length === 0 && (
            <div className="canvas-overlay-tip">
              <div className="tip-content">
                <div className="tip-icon">👆</div>
                <p>点击图片任意位置添加标记点</p>
                <p className="tip-secondary">当前选中分组：{state.groups.find(g => g.id === state.selectedGroup)?.name}</p>
              </div>
            </div>
          )}
        </Spin>
      </div>
      <div className="canvas-instructions">
        <div className="instruction-item">
          <span className="instruction-icon">🖱️</span>
          <span>点击图片添加标记点</span>
        </div>
        <div className="instruction-item">
          <span className="instruction-icon">✋</span>
          <span>拖拽标记点移动位置</span>
        </div>
        <div className="instruction-item">
          <span className="instruction-icon">🎯</span>
          <span>点击标记点选择，支持多选</span>
        </div>
        {state.selectedGroup && (
          <div className="current-group-info">
            <span
              className="group-indicator"
              data-color={state.groups.find(g => g.id === state.selectedGroup)?.color}
            ></span>
            <span>当前分组：{state.groups.find(g => g.id === state.selectedGroup)?.name}</span>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ImageCanvas;
