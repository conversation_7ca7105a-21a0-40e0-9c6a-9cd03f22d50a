.image-canvas-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-canvas-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: auto;
  position: relative;
  padding: 16px;
}

.fabric-canvas {
  border: 2px solid #e8e8e8;
  border-radius: 6px;
  background: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
}

.fabric-canvas:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

/* 画布工具提示 */
.canvas-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border: 2px dashed transparent;
  border-radius: 8px;
  transition: border-color 0.3s ease;
}

.canvas-container:hover::before {
  border-color: #1890ff;
}

.canvas-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  color: #666;
  max-width: 300px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.placeholder-content h3 {
  color: #333;
  margin: 16px 0 8px;
  font-size: 18px;
  font-weight: 600;
}

.placeholder-content > p {
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
}

.placeholder-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid #1890ff;
}

.placeholder-tips p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

/* 画布头部样式 */
.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.canvas-status {
  font-size: 12px;
  color: #666;
}

.marker-count {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* 画布覆盖提示 */
.canvas-overlay-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  text-align: center;
  pointer-events: none;
  z-index: 10;
  animation: fadeInOut 3s ease-in-out;
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.tip-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.tip-secondary {
  font-size: 12px;
  opacity: 0.9;
  margin: 0;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  20%, 80% { opacity: 1; }
}

/* 操作指引样式 */
.canvas-instructions {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.instruction-item {
  display: flex;
  align-items: center;
  margin: 4px 0;
  font-size: 11px;
  color: #666;
  line-height: 1.4;
}

.instruction-icon {
  margin-right: 6px;
  font-size: 12px;
}

.current-group-info {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px solid #e8e8e8;
  font-size: 11px;
  color: #333;
  font-weight: 500;
}

.group-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  background-color: #1890ff;
}

.group-indicator[data-color="red"] { background-color: #ff4d4f; }
.group-indicator[data-color="blue"] { background-color: #1890ff; }
.group-indicator[data-color="green"] { background-color: #52c41a; }
.group-indicator[data-color="orange"] { background-color: #fa8c16; }
.group-indicator[data-color="purple"] { background-color: #722ed1; }
.group-indicator[data-color="cyan"] { background-color: #13c2c2; }
.group-indicator[data-color="pink"] { background-color: #eb2f96; }
.group-indicator[data-color="yellow"] { background-color: #fadb14; }

/* 加载状态样式 */
.ant-spin-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-container {
    min-height: 300px;
  }
  
  .canvas-instructions {
    font-size: 11px;
  }
  
  .canvas-instructions p {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .image-canvas-card .ant-card-body {
    padding: 12px;
  }
  
  .canvas-container {
    min-height: 250px;
  }
}
