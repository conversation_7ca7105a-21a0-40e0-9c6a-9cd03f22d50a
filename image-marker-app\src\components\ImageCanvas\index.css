.image-canvas-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-canvas-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: auto;
  position: relative;
}

.fabric-canvas {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.canvas-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  color: #999;
  font-size: 16px;
}

.canvas-instructions {
  margin-top: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.canvas-instructions p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.canvas-instructions p:last-child {
  margin-bottom: 0;
}

/* 加载状态样式 */
.ant-spin-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-container {
    min-height: 300px;
  }
  
  .canvas-instructions {
    font-size: 11px;
  }
  
  .canvas-instructions p {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .image-canvas-card .ant-card-body {
    padding: 12px;
  }
  
  .canvas-container {
    min-height: 250px;
  }
}
