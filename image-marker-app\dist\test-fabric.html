<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric.js 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .loading { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        canvas {
            border: 1px solid #d9d9d9;
            margin: 10px 0;
        }
        .log {
            background: #f6f6f6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fabric.js API 测试</h1>
        
        <div id="fabric-status" class="status loading">正在加载Fabric.js...</div>
        
        <button id="btn-test" onclick="testImageLoad()" disabled>测试图片加载</button>
        <button onclick="testCanvas()" disabled id="btn-canvas">测试Canvas</button>
        
        <canvas id="test-canvas" width="600" height="400"></canvas>
        
        <div id="test-log" class="log"></div>
    </div>

    <!-- 使用修复后的Fabric.js v5 -->
    <script>
        let fabricCanvas = null;
        let fabricReady = false;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'loading') {
            const statusElement = document.getElementById('fabric-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function enableButtons() {
            document.getElementById('btn-test').disabled = false;
            document.getElementById('btn-canvas').disabled = false;
        }

        // 加载Fabric.js v5
        function loadFabricJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        log('✅ Fabric.js v5 loaded successfully');
                        log(`Fabric.js version: ${fabric.version}`);
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load Fabric.js'));
                script.src = 'https://unpkg.com/fabric@5.3.0/dist/fabric.min.js';
                document.head.appendChild(script);
            });
        }

        function initializeCanvas() {
            try {
                const canvasElement = document.getElementById('test-canvas');
                fabricCanvas = new fabric.Canvas(canvasElement, {
                    selection: true,
                    backgroundColor: '#f5f5f5'
                });
                
                log('✅ Canvas初始化成功');
                log(`Canvas尺寸: ${fabricCanvas.width}x${fabricCanvas.height}`);
                
                enableButtons();
                updateStatus('✅ Fabric.js v5加载完成，可以开始测试', 'success');
                
            } catch (error) {
                log(`❌ Canvas初始化失败: ${error.message}`);
                updateStatus('❌ Canvas初始化失败', 'error');
            }
        }

        function testImageLoad() {
            if (!fabricCanvas) {
                log('❌ Canvas未初始化');
                return;
            }

            try {
                log('开始测试图片加载...');
                
                // 创建测试SVG
                const svgContent = `
                    <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="#e6f7ff" />
                        <rect x="50" y="50" width="300" height="200" fill="white" stroke="#1890ff" stroke-width="2" rx="10"/>
                        <text x="200" y="120" text-anchor="middle" font-family="Arial" font-size="18" fill="#1890ff">
                            Fabric.js v5 测试图片
                        </text>
                        <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
                            图片加载成功！
                        </text>
                        <circle cx="120" cy="200" r="6" fill="#52c41a"/>
                        <text x="140" y="205" font-family="Arial" font-size="12" fill="#52c41a">测试标记</text>
                    </svg>
                `;

                const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);

                // 使用Fabric.js v5的API
                fabric.Image.fromURL(url, function(img) {
                    log(`✅ 图片加载成功: ${img.width}x${img.height}`);
                    
                    fabricCanvas.clear();
                    
                    // 计算缩放
                    const scale = Math.min(
                        (fabricCanvas.width - 40) / img.width,
                        (fabricCanvas.height - 40) / img.height,
                        1
                    );
                    
                    img.set({
                        left: (fabricCanvas.width - img.width * scale) / 2,
                        top: (fabricCanvas.height - img.height * scale) / 2,
                        scaleX: scale,
                        scaleY: scale,
                        selectable: false,
                        evented: false
                    });
                    
                    fabricCanvas.add(img);
                    fabricCanvas.sendObjectToBack(img);
                    fabricCanvas.renderAll();
                    
                    log('✅ 图片添加到Canvas成功');
                    URL.revokeObjectURL(url);
                    
                }, {
                    crossOrigin: 'anonymous'
                });
                
            } catch (error) {
                log(`❌ 图片加载测试失败: ${error.message}`);
            }
        }

        function testCanvas() {
            if (!fabricCanvas) {
                log('❌ Canvas未初始化');
                return;
            }

            try {
                const x = 100 + Math.random() * 400;
                const y = 100 + Math.random() * 200;
                
                const circle = new fabric.Circle({
                    left: x - 5,
                    top: y - 5,
                    radius: 5,
                    fill: '#ff4d4f',
                    stroke: '#ffffff',
                    strokeWidth: 2,
                    selectable: true
                });

                const text = new fabric.Text(`测试${fabricCanvas.getObjects().length + 1}`, {
                    left: x + 8,
                    top: y - 8,
                    fontSize: 12,
                    fill: '#ff4d4f',
                    backgroundColor: 'rgba(255, 255, 255, 0.8)'
                });

                fabricCanvas.add(circle);
                fabricCanvas.add(text);
                fabricCanvas.renderAll();

                log(`✅ 添加测试对象成功: (${Math.round(x)}, ${Math.round(y)})`);
                
            } catch (error) {
                log(`❌ 添加对象失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动加载Fabric.js
        window.onload = function() {
            log('页面加载完成，开始加载Fabric.js v5...');
            updateStatus('正在加载Fabric.js v5...', 'loading');
            
            loadFabricJS().then(() => {
                log('✅ Fabric.js v5加载完成');
                fabricReady = true;
                initializeCanvas();
            }).catch((error) => {
                log('❌ Fabric.js加载失败: ' + error.message);
                updateStatus('❌ Fabric.js加载失败', 'error');
            });
        };
    </script>
</body>
</html>
