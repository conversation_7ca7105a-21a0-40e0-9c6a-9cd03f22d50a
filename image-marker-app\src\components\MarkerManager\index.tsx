import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Popconfirm,
  Modal,
  Form,
  Input,
  Select,
  ColorPicker,
  message,
  Typography,
  Tooltip,
  Tag
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useApp } from '../../context/AppContext';
import type { MarkerPoint } from '../../types';
import './index.css';

const { Title } = Typography;
const { Option } = Select;

interface EditModalProps {
  visible: boolean;
  marker: MarkerPoint | null;
  onCancel: () => void;
  onOk: (values: any) => void;
}

const EditModal: React.FC<EditModalProps> = ({ visible, marker, onCancel, onOk }) => {
  const { state } = useApp();
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (marker) {
      form.setFieldsValue({
        name: marker.name,
        groupId: marker.groupId,
        color: marker.color,
        x: marker.x,
        y: marker.y
      });
    }
  }, [marker, form]);

  const handleOk = () => {
    form.validateFields().then(values => {
      onOk(values);
      form.resetFields();
    });
  };

  return (
    <Modal
      title="编辑标记"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      okText="确定"
      cancelText="取消"
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="标记名称"
          rules={[{ required: true, message: '请输入标记名称' }]}
        >
          <Input placeholder="请输入标记名称" />
        </Form.Item>
        
        <Form.Item
          name="groupId"
          label="所属分组"
          rules={[{ required: true, message: '请选择分组' }]}
        >
          <Select placeholder="请选择分组">
            {state.groups.map(group => (
              <Option key={group.id} value={group.id}>
                <Space>
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      backgroundColor: group.color,
                      borderRadius: '50%'
                    }}
                  />
                  {group.name}
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="color"
          label="标记颜色"
          rules={[{ required: true, message: '请选择颜色' }]}
        >
          <ColorPicker showText />
        </Form.Item>

        <Space.Compact style={{ width: '100%' }}>
          <Form.Item
            name="x"
            label="X坐标"
            style={{ width: '50%' }}
            rules={[{ required: true, message: '请输入X坐标' }]}
          >
            <Input type="number" placeholder="X坐标" />
          </Form.Item>
          <Form.Item
            name="y"
            label="Y坐标"
            style={{ width: '50%' }}
            rules={[{ required: true, message: '请输入Y坐标' }]}
          >
            <Input type="number" placeholder="Y坐标" />
          </Form.Item>
        </Space.Compact>
      </Form>
    </Modal>
  );
};

const MarkerManager: React.FC = () => {
  const { state, updateMarker, deleteMarker, deleteSelectedMarkers, toggleMarkerVisibility, selectMarkers } = useApp();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingMarker, setEditingMarker] = useState<MarkerPoint | null>(null);

  // 获取分组信息
  const getGroupInfo = (groupId: string) => {
    return state.groups.find(group => group.id === groupId);
  };

  // 处理编辑
  const handleEdit = (marker: MarkerPoint) => {
    setEditingMarker(marker);
    setEditModalVisible(true);
  };

  // 处理编辑确认
  const handleEditOk = (values: any) => {
    if (editingMarker) {
      updateMarker(editingMarker.id, {
        name: values.name,
        groupId: values.groupId,
        color: typeof values.color === 'string' ? values.color : values.color.toHexString(),
        x: parseFloat(values.x),
        y: parseFloat(values.y)
      });
      message.success('标记更新成功');
    }
    setEditModalVisible(false);
    setEditingMarker(null);
  };

  // 处理删除
  const handleDelete = (markerId: string) => {
    deleteMarker(markerId);
    message.success('标记删除成功');
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (state.selectedMarkers.length === 0) {
      message.warning('请先选择要删除的标记');
      return;
    }
    deleteSelectedMarkers();
    message.success(`已删除 ${state.selectedMarkers.length} 个标记`);
  };

  // 处理可见性切换
  const handleToggleVisibility = (markerId: string) => {
    toggleMarkerVisibility(markerId);
  };

  // 表格列定义
  const columns: ColumnsType<MarkerPoint> = [
    {
      title: '标记名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true
    },
    {
      title: '坐标',
      key: 'coordinates',
      width: 120,
      render: (_, record) => (
        <span>({record.x.toFixed(0)}, {record.y.toFixed(0)})</span>
      )
    },
    {
      title: '分组',
      key: 'group',
      width: 120,
      render: (_, record) => {
        const group = getGroupInfo(record.groupId);
        return group ? (
          <Tag color={group.color}>{group.name}</Tag>
        ) : (
          <Tag>未知分组</Tag>
        );
      }
    },
    {
      title: '颜色',
      key: 'color',
      width: 80,
      render: (_, record) => (
        <div
          style={{
            width: 20,
            height: 20,
            backgroundColor: record.color,
            borderRadius: '50%',
            border: '1px solid #d9d9d9'
          }}
        />
      )
    },
    {
      title: '状态',
      key: 'visible',
      width: 80,
      render: (_, record) => (
        <Tooltip title={record.visible ? '点击隐藏' : '点击显示'}>
          <Button
            type="text"
            size="small"
            icon={record.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={() => handleToggleVisibility(record.id)}
            style={{ color: record.visible ? '#52c41a' : '#999' }}
          />
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个标记吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: state.selectedMarkers,
    onChange: (selectedRowKeys: React.Key[]) => {
      selectMarkers(selectedRowKeys as string[]);
    }
  };

  return (
    <Card className="marker-manager">
      <div className="manager-header">
        <Title level={5} style={{ margin: 0 }}>
          标记管理 ({state.markers.length})
        </Title>
        <Space>
          <Popconfirm
            title={`确定要删除选中的 ${state.selectedMarkers.length} 个标记吗？`}
            onConfirm={handleBatchDelete}
            disabled={state.selectedMarkers.length === 0}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              disabled={state.selectedMarkers.length === 0}
              icon={<DeleteOutlined />}
            >
              批量删除 ({state.selectedMarkers.length})
            </Button>
          </Popconfirm>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={state.markers}
        rowKey="id"
        size="small"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
        }}
        rowSelection={rowSelection}
        scroll={{ y: 400 }}
      />

      <EditModal
        visible={editModalVisible}
        marker={editingMarker}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingMarker(null);
        }}
        onOk={handleEditOk}
      />
    </Card>
  );
};

export default MarkerManager;
