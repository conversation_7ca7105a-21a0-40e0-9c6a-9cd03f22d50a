import React, { createContext, useContext, useReducer } from 'react';
import type { ReactNode } from 'react';
import type { AppState, AppAction, MarkerGroup, MarkerPoint } from '../types';
import { generateId, generateRandomColor } from '../utils';

// 初始状态
const initialState: AppState = {
  currentImage: null,
  markers: [],
  groups: [
    {
      id: 'default',
      name: '默认分组',
      color: '#1890ff',
      visible: true,
      createdAt: new Date()
    }
  ],
  selectedMarkers: [],
  selectedGroup: 'default',
  isLoading: false,
  error: null
};

// Reducer函数
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_IMAGE':
      return {
        ...state,
        currentImage: action.payload,
        markers: [], // 切换图片时清空标记
        selectedMarkers: []
      };

    case 'ADD_MARKER':
      return {
        ...state,
        markers: [...state.markers, action.payload]
      };

    case 'UPDATE_MARKER':
      return {
        ...state,
        markers: state.markers.map(marker =>
          marker.id === action.payload.id
            ? { ...marker, ...action.payload.updates, updatedAt: new Date() }
            : marker
        )
      };

    case 'DELETE_MARKER':
      return {
        ...state,
        markers: state.markers.filter(marker => marker.id !== action.payload),
        selectedMarkers: state.selectedMarkers.filter(id => id !== action.payload)
      };

    case 'DELETE_MARKERS':
      return {
        ...state,
        markers: state.markers.filter(marker => !action.payload.includes(marker.id)),
        selectedMarkers: state.selectedMarkers.filter(id => !action.payload.includes(id))
      };

    case 'ADD_GROUP':
      return {
        ...state,
        groups: [...state.groups, action.payload]
      };

    case 'UPDATE_GROUP':
      return {
        ...state,
        groups: state.groups.map(group =>
          group.id === action.payload.id
            ? { ...group, ...action.payload.updates }
            : group
        )
      };

    case 'DELETE_GROUP':
      // 删除分组时，将该分组的标记移动到默认分组
      const updatedMarkers = state.markers.map(marker =>
        marker.groupId === action.payload
          ? { ...marker, groupId: 'default', updatedAt: new Date() }
          : marker
      );
      
      return {
        ...state,
        groups: state.groups.filter(group => group.id !== action.payload),
        markers: updatedMarkers,
        selectedGroup: state.selectedGroup === action.payload ? 'default' : state.selectedGroup
      };

    case 'SELECT_MARKERS':
      return {
        ...state,
        selectedMarkers: action.payload
      };

    case 'SELECT_GROUP':
      return {
        ...state,
        selectedGroup: action.payload
      };

    case 'TOGGLE_MARKER_VISIBILITY':
      return {
        ...state,
        markers: state.markers.map(marker =>
          marker.id === action.payload
            ? { ...marker, visible: !marker.visible, updatedAt: new Date() }
            : marker
        )
      };

    case 'TOGGLE_GROUP_VISIBILITY':
      const group = state.groups.find(g => g.id === action.payload);
      if (!group) return state;

      const newVisibility = !group.visible;
      
      return {
        ...state,
        groups: state.groups.map(g =>
          g.id === action.payload ? { ...g, visible: newVisibility } : g
        ),
        markers: state.markers.map(marker =>
          marker.groupId === action.payload
            ? { ...marker, visible: newVisibility, updatedAt: new Date() }
            : marker
        )
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      };

    case 'IMPORT_DATA':
      return {
        ...state,
        markers: [...state.markers, ...action.payload.markers],
        groups: [...state.groups, ...action.payload.groups]
      };

    case 'CLEAR_ALL':
      return {
        ...initialState,
        groups: state.groups.filter(group => group.id === 'default') // 保留默认分组
      };

    default:
      return state;
  }
};

// Context类型定义
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // 便捷方法
  addMarker: (x: number, y: number, name?: string) => void;
  updateMarker: (id: string, updates: Partial<MarkerPoint>) => void;
  deleteMarker: (id: string) => void;
  deleteSelectedMarkers: () => void;
  addGroup: (name: string, description?: string) => string;
  updateGroup: (id: string, updates: Partial<MarkerGroup>) => void;
  deleteGroup: (id: string) => void;
  toggleMarkerVisibility: (id: string) => void;
  toggleGroupVisibility: (id: string) => void;
  selectMarkers: (ids: string[]) => void;
  selectGroup: (id: string | null) => void;
  clearAll: () => void;
}

// 创建Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider组件
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 便捷方法实现
  const addMarker = (x: number, y: number, name?: string) => {
    const selectedGroup = state.groups.find(g => g.id === state.selectedGroup) || state.groups[0];
    const marker = {
      id: generateId(),
      x,
      y,
      name: name || `标记${state.markers.length + 1}`,
      groupId: selectedGroup.id,
      visible: true,
      color: selectedGroup.color,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    dispatch({ type: 'ADD_MARKER', payload: marker });
  };

  const updateMarker = (id: string, updates: Partial<MarkerPoint>) => {
    dispatch({ type: 'UPDATE_MARKER', payload: { id, updates } });
  };

  const deleteMarker = (id: string) => {
    dispatch({ type: 'DELETE_MARKER', payload: id });
  };

  const deleteSelectedMarkers = () => {
    if (state.selectedMarkers.length > 0) {
      dispatch({ type: 'DELETE_MARKERS', payload: state.selectedMarkers });
    }
  };

  const addGroup = (name: string, description?: string): string => {
    const group: MarkerGroup = {
      id: generateId(),
      name,
      color: generateRandomColor(),
      visible: true,
      description,
      createdAt: new Date()
    };
    dispatch({ type: 'ADD_GROUP', payload: group });
    return group.id;
  };

  const updateGroup = (id: string, updates: Partial<MarkerGroup>) => {
    dispatch({ type: 'UPDATE_GROUP', payload: { id, updates } });
  };

  const deleteGroup = (id: string) => {
    if (id !== 'default') { // 不能删除默认分组
      dispatch({ type: 'DELETE_GROUP', payload: id });
    }
  };

  const toggleMarkerVisibility = (id: string) => {
    dispatch({ type: 'TOGGLE_MARKER_VISIBILITY', payload: id });
  };

  const toggleGroupVisibility = (id: string) => {
    dispatch({ type: 'TOGGLE_GROUP_VISIBILITY', payload: id });
  };

  const selectMarkers = (ids: string[]) => {
    dispatch({ type: 'SELECT_MARKERS', payload: ids });
  };

  const selectGroup = (id: string | null) => {
    dispatch({ type: 'SELECT_GROUP', payload: id });
  };

  const clearAll = () => {
    dispatch({ type: 'CLEAR_ALL' });
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    addMarker,
    updateMarker,
    deleteMarker,
    deleteSelectedMarkers,
    addGroup,
    updateGroup,
    deleteGroup,
    toggleMarkerVisibility,
    toggleGroupVisibility,
    selectMarkers,
    selectGroup,
    clearAll
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Hook for using the context
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
