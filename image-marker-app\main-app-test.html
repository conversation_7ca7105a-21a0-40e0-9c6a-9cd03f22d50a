<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主应用功能验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #001529;
            color: white;
            padding: 16px 24px;
            font-size: 18px;
            font-weight: 500;
        }
        .content {
            display: flex;
            min-height: 600px;
        }
        .sidebar {
            width: 300px;
            background: #fafafa;
            border-right: 1px solid #f0f0f0;
            padding: 24px;
        }
        .main-area {
            flex: 1;
            padding: 24px;
            display: flex;
            flex-direction: column;
        }
        .canvas-container {
            flex: 1;
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }
        .canvas-container.has-image {
            border-style: solid;
            border-color: #1890ff;
            background: white;
        }
        .empty-state {
            text-align: center;
            color: #999;
        }
        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d9d9d9;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px 0;
            width: 100%;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .example-btn {
            background: #52c41a;
            font-weight: 500;
        }
        .example-btn:hover {
            background: #73d13d;
        }
        .file-input {
            margin: 16px 0;
        }
        .file-input input {
            width: 100%;
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .info-section {
            background: #f6f6f6;
            padding: 16px;
            border-radius: 4px;
            margin: 16px 0;
            font-size: 12px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 0;
            font-size: 12px;
        }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .status.warning { background: #fffbe6; border: 1px solid #ffe58f; color: #faad14; }
        .status.loading { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
        canvas {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .log {
            background: #f6f6f6;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            max-height: 150px;
            overflow-y: auto;
            margin-top: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            🖼️ 图片标记管理系统 - 主应用功能验证
        </div>
        
        <div class="content">
            <div class="sidebar">
                <h3>📋 当前状态</h3>
                <div id="fabric-status" class="status loading">正在加载Fabric.js...</div>
                
                <h3>🎯 快速操作</h3>
                <button id="btn-example" class="example-btn" onclick="loadExampleImage()" disabled>
                    🎯 加载示例图片试用
                </button>
                
                <div class="file-input">
                    <label>📁 上传图片文件：</label>
                    <input type="file" id="file-input" accept="image/*" onchange="loadUserImage(this)" disabled>
                </div>
                
                <h3>🔧 画布操作</h3>
                <button onclick="addTestMarker()" disabled id="btn-marker">添加测试标记</button>
                <button onclick="clearCanvas()" disabled id="btn-clear">清空画布</button>
                
                <div class="info-section">
                    <strong>📊 当前信息：</strong><br>
                    文件名: <span id="info-filename">未加载</span><br>
                    尺寸: <span id="info-size">-</span><br>
                    标记数: <span id="info-markers">0</span>
                </div>
                
                <div class="log" id="test-log"></div>
            </div>
            
            <div class="main-area">
                <h3>🎨 图片标记画布</h3>
                <div class="canvas-container" id="canvas-container">
                    <div class="empty-state" id="empty-state">
                        <div class="icon">🖼️</div>
                        <div>点击左侧"🎯 加载示例图片试用"开始使用</div>
                        <div style="font-size: 12px; color: #ccc; margin-top: 8px;">
                            或拖拽图片文件到此区域
                        </div>
                    </div>
                    <canvas id="main-canvas" width="800" height="600" style="display: none;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Fabric.js CDN加载 - 与修复后的主应用相同 -->
    <script>
        let fabricCanvas = null;
        let fabricReady = false;
        let markerCount = 0;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'loading') {
            const statusElement = document.getElementById('fabric-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function updateInfo(filename = '未加载', size = '-', markers = 0) {
            document.getElementById('info-filename').textContent = filename;
            document.getElementById('info-size').textContent = size;
            document.getElementById('info-markers').textContent = markers;
        }

        function enableButtons() {
            document.getElementById('btn-example').disabled = false;
            document.getElementById('btn-marker').disabled = false;
            document.getElementById('btn-clear').disabled = false;
            document.getElementById('file-input').disabled = false;
        }

        function showCanvas() {
            document.getElementById('empty-state').style.display = 'none';
            document.getElementById('main-canvas').style.display = 'block';
            document.getElementById('canvas-container').classList.add('has-image');
        }

        function hideCanvas() {
            document.getElementById('empty-state').style.display = 'block';
            document.getElementById('main-canvas').style.display = 'none';
            document.getElementById('canvas-container').classList.remove('has-image');
        }

        // 加载Fabric.js的函数 - 与修复后的主应用相同
        function loadFabricJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        log('✅ Fabric.js loaded successfully from primary CDN');
                        log(`Fabric.js version: ${fabric.version}`);
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from primary CDN'));
                script.src = 'https://unpkg.com/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        // 备用CDN
        function loadFabricJSFallback() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        log('✅ Fabric.js loaded successfully from fallback CDN');
                        log(`Fabric.js version: ${fabric.version}`);
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from fallback CDN'));
                script.src = 'https://cdn.jsdelivr.net/npm/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        function initializeCanvas() {
            try {
                const canvasElement = document.getElementById('main-canvas');
                fabricCanvas = new fabric.Canvas(canvasElement, {
                    selection: true,
                    backgroundColor: '#f5f5f5'
                });
                
                log('✅ Canvas初始化成功');
                log(`Canvas尺寸: ${fabricCanvas.width}x${fabricCanvas.height}`);
                
                // 添加点击事件监听 - 模拟主应用的标记添加功能
                fabricCanvas.on('mouse:down', function(e) {
                    if (!e.target) {
                        const pointer = fabricCanvas.getPointer(e.e);
                        addMarkerAtPosition(pointer.x, pointer.y);
                    }
                });
                
                enableButtons();
                updateStatus('✅ Fabric.js加载完成，可以开始使用', 'success');
                
            } catch (error) {
                log(`❌ Canvas初始化失败: ${error.message}`);
                updateStatus('❌ Canvas初始化失败', 'error');
            }
        }

        function loadExampleImage() {
            if (!fabricCanvas) {
                log('❌ Canvas未初始化');
                return;
            }

            try {
                log('开始加载示例图片...');
                updateStatus('正在加载示例图片...', 'loading');
                
                // 创建与主应用相同的示例SVG
                const svgContent = `
                    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#e6f7ff;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#f0f8ff;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#bg)" />
                        <rect x="100" y="100" width="600" height="400" fill="white" stroke="#1890ff" stroke-width="3" rx="15"/>
                        <text x="400" y="200" text-anchor="middle" font-family="Arial" font-size="24" fill="#1890ff" font-weight="bold">
                            🎯 示例图片 - 图片标记测试
                        </text>
                        <text x="400" y="250" text-anchor="middle" font-family="Arial" font-size="16" fill="#666">
                            点击图片任意位置添加标记点
                        </text>
                        <text x="400" y="280" text-anchor="middle" font-family="Arial" font-size="14" fill="#999">
                            拖拽标记点可以移动位置
                        </text>
                        <circle cx="200" cy="350" r="8" fill="#52c41a" opacity="0.8"/>
                        <text x="220" y="355" font-family="Arial" font-size="12" fill="#52c41a">示例标记点</text>
                        <circle cx="600" cy="350" r="8" fill="#ff4d4f" opacity="0.8"/>
                        <text x="520" y="355" font-family="Arial" font-size="12" fill="#ff4d4f">另一个标记</text>
                    </svg>
                `;

                const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);

                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(`✅ 示例图片加载成功: ${img.width}x${img.height}`);
                    
                    fabricCanvas.clear();
                    markerCount = 0;
                    
                    img.set({
                        left: 0,
                        top: 0,
                        selectable: false,
                        evented: false
                    });
                    
                    fabricCanvas.add(img);
                    fabricCanvas.sendObjectToBack(img);
                    fabricCanvas.renderAll();
                    
                    showCanvas();
                    updateInfo('示例图片.svg', `${img.width}x${img.height}`, markerCount);
                    updateStatus('✅ 示例图片加载成功，点击图片添加标记', 'success');
                    log('✅ 示例图片添加到Canvas成功');
                    URL.revokeObjectURL(url);
                    
                }).catch((error) => {
                    log(`❌ 示例图片加载失败: ${error.message}`);
                    updateStatus('❌ 示例图片加载失败', 'error');
                    URL.revokeObjectURL(url);
                });
                
            } catch (error) {
                log(`❌ 示例图片测试失败: ${error.message}`);
                updateStatus('❌ 示例图片测试失败', 'error');
            }
        }

        function addMarkerAtPosition(x, y) {
            if (!fabricCanvas) return;

            try {
                markerCount++;
                const colors = ['#ff4d4f', '#52c41a', '#1890ff', '#fa8c16', '#722ed1'];
                const color = colors[markerCount % colors.length];
                
                const circle = new fabric.Circle({
                    left: x - 5,
                    top: y - 5,
                    radius: 5,
                    fill: color,
                    stroke: '#ffffff',
                    strokeWidth: 2,
                    selectable: true
                });

                const text = new fabric.Text(`标记${markerCount}`, {
                    left: x + 8,
                    top: y - 8,
                    fontSize: 12,
                    fill: color,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)'
                });

                fabricCanvas.add(circle);
                fabricCanvas.add(text);
                fabricCanvas.renderAll();

                updateInfo(
                    document.getElementById('info-filename').textContent,
                    document.getElementById('info-size').textContent,
                    markerCount
                );

                log(`✅ 添加标记点${markerCount}: (${Math.round(x)}, ${Math.round(y)})`);
                
            } catch (error) {
                log(`❌ 添加标记点失败: ${error.message}`);
            }
        }

        function addTestMarker() {
            const x = 200 + Math.random() * 400;
            const y = 200 + Math.random() * 200;
            addMarkerAtPosition(x, y);
        }

        function clearCanvas() {
            if (!fabricCanvas) return;

            try {
                fabricCanvas.clear();
                fabricCanvas.backgroundColor = '#f5f5f5';
                fabricCanvas.renderAll();
                markerCount = 0;
                hideCanvas();
                updateInfo();
                updateStatus('✅ 画布已清空', 'success');
                log('✅ Canvas已清空');
            } catch (error) {
                log(`❌ 清空Canvas失败: ${error.message}`);
            }
        }

        function loadUserImage(input) {
            const file = input.files[0];
            if (!file || !fabricCanvas) return;

            try {
                log(`开始加载用户图片: ${file.name} (${file.size} bytes)`);
                updateStatus('正在加载用户图片...', 'loading');
                
                const url = URL.createObjectURL(file);
                
                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(`✅ 用户图片加载成功: ${img.width}x${img.height}`);
                    
                    fabricCanvas.clear();
                    markerCount = 0;
                    
                    // 计算缩放以适应画布
                    const scale = Math.min(
                        fabricCanvas.width / img.width,
                        fabricCanvas.height / img.height,
                        1
                    );
                    
                    img.set({
                        left: (fabricCanvas.width - img.width * scale) / 2,
                        top: (fabricCanvas.height - img.height * scale) / 2,
                        scaleX: scale,
                        scaleY: scale,
                        selectable: false,
                        evented: false
                    });
                    
                    fabricCanvas.add(img);
                    fabricCanvas.sendObjectToBack(img);
                    fabricCanvas.renderAll();
                    
                    showCanvas();
                    updateInfo(file.name, `${img.width}x${img.height}`, markerCount);
                    updateStatus('✅ 用户图片加载成功，点击图片添加标记', 'success');
                    log('✅ 用户图片添加到Canvas成功');
                    URL.revokeObjectURL(url);
                    
                }).catch((error) => {
                    log(`❌ 用户图片加载失败: ${error.message}`);
                    updateStatus('❌ 用户图片加载失败', 'error');
                    URL.revokeObjectURL(url);
                });
                
            } catch (error) {
                log(`❌ 用户图片测试失败: ${error.message}`);
                updateStatus('❌ 用户图片测试失败', 'error');
            }
        }

        // 页面加载完成后自动加载Fabric.js - 与修复后的主应用相同
        window.onload = function() {
            log('页面加载完成，开始加载Fabric.js...');
            updateStatus('正在加载Fabric.js...', 'loading');
            
            loadFabricJS().catch(() => {
                log('主CDN失败，尝试备用CDN...');
                updateStatus('主CDN失败，尝试备用CDN...', 'warning');
                return loadFabricJSFallback();
            }).then(() => {
                log('✅ Fabric.js加载完成');
                fabricReady = true;
                initializeCanvas();
            }).catch((error) => {
                log('❌ 所有CDN都失败');
                updateStatus('❌ Fabric.js加载失败，请刷新页面重试', 'error');
            });
        };

        // 拖拽上传功能
        const canvasContainer = document.getElementById('canvas-container');
        
        canvasContainer.addEventListener('dragover', (e) => {
            e.preventDefault();
            canvasContainer.style.borderColor = '#1890ff';
            canvasContainer.style.backgroundColor = '#f0f8ff';
        });
        
        canvasContainer.addEventListener('dragleave', (e) => {
            e.preventDefault();
            canvasContainer.style.borderColor = '#d9d9d9';
            canvasContainer.style.backgroundColor = '#fafafa';
        });
        
        canvasContainer.addEventListener('drop', (e) => {
            e.preventDefault();
            canvasContainer.style.borderColor = '#d9d9d9';
            canvasContainer.style.backgroundColor = '#fafafa';
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                const fileInput = document.getElementById('file-input');
                fileInput.files = files;
                loadUserImage(fileInput);
            }
        });
    </script>
</body>
</html>
