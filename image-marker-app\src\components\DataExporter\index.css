.data-exporter {
  height: 100%;
}

.data-exporter .ant-card-body {
  padding: 20px;
}

.export-section,
.import-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.section-content {
  padding-left: 24px;
}

.section-content .ant-space {
  width: 100%;
}

.section-content .ant-alert {
  border-radius: 6px;
}

.section-content .ant-alert ul {
  color: #666;
  font-size: 12px;
}

.section-content .ant-alert li {
  margin-bottom: 4px;
}

/* 上传按钮样式 */
.data-exporter .ant-upload {
  display: inline-block;
}

.data-exporter .ant-btn {
  border-radius: 6px;
}

/* 进度模态框样式 */
.data-exporter .ant-modal-body {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-exporter .ant-card-body {
    padding: 16px;
  }
  
  .section-content {
    padding-left: 16px;
  }
  
  .section-content .ant-space {
    flex-direction: column;
    align-items: stretch;
  }
  
  .section-content .ant-alert {
    font-size: 12px;
  }
  
  .section-content .ant-alert ul {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .data-exporter .ant-card-body {
    padding: 12px;
  }
  
  .section-header {
    font-size: 13px;
  }
  
  .section-content {
    padding-left: 12px;
  }
}
