!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).fabric={})}(this,(function(t){"use strict";function e(t,e,s){return(e=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var s=t[Symbol.toPrimitive];if(void 0!==s){var i=s.call(t,e||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function s(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,i)}return s}function i(t){for(var i=1;i=0)continue;s[i]=t[i]}return s}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(i=0;i=0||{}.propertyIsEnumerable.call(t,s)&&(r[s]=t[s])}return r}function n(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}class o{constructor(){e(this,"browserShadowBlurConstant",1),e(this,"DPI",96),e(this,"devicePixelRatio","undefined"!=typeof window?window.devicePixelRatio:1),e(this,"perfLimitSizeTotal",2097152),e(this,"maxCacheSideLimit",4096),e(this,"minCacheSideLimit",256),e(this,"disableStyleCopyPaste",!1),e(this,"enableGLFiltering",!0),e(this,"textureSize",4096),e(this,"forceGLPutImageData",!1),e(this,"cachesBoundsOfCurve",!1),e(this,"fontPaths",{}),e(this,"NUM_FRACTION_DIGITS",4)}}const a=new class extends o{constructor(t){super(),this.configure(t)}configure(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object.assign(this,t)}addFonts(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fontPaths=i(i({},this.fontPaths),t)}removeFonts(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((t=>{delete this.fontPaths[t]}))}clearFonts(){this.fontPaths={}}restoreDefaults(t){const e=new o,s=(null==t?void 0:t.reduce(((t,s)=>(t[s]=e[s],t)),{}))||e;this.configure(s)}},h=function(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),i=1;ithis.testPrecision(e,t))),e.getExtension("WEBGL_lose_context").loseContext(),h("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(t){return!!this.maxTextureSize&&this.maxTextureSize>=t}}const g={};let f;const p=()=>f||(f={document:document,window:window,isTouchSupported:"ontouchstart"in window||"ontouchstart"in document||window&&window.navigator&&window.navigator.maxTouchPoints>0,WebGLProbe:new d,dispose(){},copyPasteData:g}),m=()=>p().document,v=()=>p().window,y=()=>{var t;return Math.max(null!==(t=a.devicePixelRatio)&&void 0!==t?t:v().devicePixelRatio,1)};const _=new class{constructor(){e(this,"charWidthsCache",{}),e(this,"boundsOfCurveCache",{})}getFontCache(t){let{fontFamily:e,fontStyle:s,fontWeight:i}=t;e=e.toLowerCase(),this.charWidthsCache[e]||(this.charWidthsCache[e]={});const r=this.charWidthsCache[e],n="".concat(s.toLowerCase(),"_").concat((i+"").toLowerCase());return r[n]||(r[n]={}),r[n]}clearFontCache(t){(t=(t||"").toLowerCase())?this.charWidthsCache[t]&&delete this.charWidthsCache[t]:this.charWidthsCache={}}limitDimsByArea(t){const{perfLimitSizeTotal:e}=a,s=Math.sqrt(e*t);return[Math.floor(s),Math.floor(e/s)]}};const x="6.6.6";function C(){}const b=Math.PI/2,S=2*Math.PI,w=Math.PI/180,T=Object.freeze([1,0,0,1,0,0]),O=16,k=.4477152502,D="center",M="left",P="top",E="bottom",A="right",j="none",F=/\r?\n/,L="moving",R="scaling",B="rotating",I="rotate",X="skewing",W="resizing",Y="modifyPoly",V="modifyPath",G="changed",z="scale",H="scaleX",N="scaleY",U="skewX",q="skewY",K="fill",J="stroke",Q="modified",Z="json",$="svg";const tt=new class{constructor(){this[Z]=new Map,this[$]=new Map}has(t){return this[Z].has(t)}getClass(t){const e=this[Z].get(t);if(!e)throw new c("No class registered for ".concat(t));return e}setClass(t,e){e?this[Z].set(e,t):(this[Z].set(t.type,t),this[Z].set(t.type.toLowerCase(),t))}getSVGClass(t){return this[$].get(t)}setSVGClass(t,e){this[$].set(null!=e?e:t.type.toLowerCase(),t)}};const et=new class extends Array{remove(t){const e=this.indexOf(t);e>-1&&this.splice(e,1)}cancelAll(){const t=this.splice(0);return t.forEach((t=>t.abort())),t}cancelByCanvas(t){if(!t)return[];const e=this.filter((e=>{var s;return e.target===t||"object"==typeof e.target&&(null===(s=e.target)||void 0===s?void 0:s.canvas)===t}));return e.forEach((t=>t.abort())),e}cancelByTarget(t){if(!t)return[];const e=this.filter((e=>e.target===t));return e.forEach((t=>t.abort())),e}};class st{constructor(){e(this,"__eventListeners",{})}on(t,e){if(this.__eventListeners||(this.__eventListeners={}),"object"==typeof t)return Object.entries(t).forEach((t=>{let[e,s]=t;this.on(e,s)})),()=>this.off(t);if(e){const s=t;return this.__eventListeners[s]||(this.__eventListeners[s]=[]),this.__eventListeners[s].push(e),()=>this.off(s,e)}return()=>!1}once(t,e){if("object"==typeof t){const e=[];return Object.entries(t).forEach((t=>{let[s,i]=t;e.push(this.once(s,i))})),()=>e.forEach((t=>t()))}if(e){const s=this.on(t,(function(){for(var t=arguments.length,i=new Array(t),r=0;r!1}_removeEventListener(t,e){if(this.__eventListeners[t])if(e){const s=this.__eventListeners[t],i=s.indexOf(e);i>-1&&s.splice(i,1)}else this.__eventListeners[t]=[]}off(t,e){if(this.__eventListeners)if(void 0===t)for(const t in this.__eventListeners)this._removeEventListener(t);else"object"==typeof t?Object.entries(t).forEach((t=>{let[e,s]=t;this._removeEventListener(e,s)})):this._removeEventListener(t,e)}fire(t,e){var s;if(!this.__eventListeners)return;const i=null===(s=this.__eventListeners[t])||void 0===s?void 0:s.concat();if(i)for(let t=0;t{const s=t.indexOf(e);return-1!==s&&t.splice(s,1),t},rt=t=>{if(0===t)return 1;switch(Math.abs(t)/b){case 1:case 3:return 0;case 2:return-1}return Math.cos(t)},nt=t=>{if(0===t)return 0;const e=t/b,s=Math.sign(t);switch(e){case 1:return s;case 2:return 0;case 3:return-s}return Math.sin(t)};class ot{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;"object"==typeof t?(this.x=t.x,this.y=t.y):(this.x=t,this.y=e)}add(t){return new ot(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new ot(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new ot(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new ot(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new ot(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new ot(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new ot(this.x/t.x,this.y/t.y)}scalarDivide(t){return new ot(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.xt.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5;return e=Math.max(Math.min(1,e),0),new ot(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)}distanceFrom(t){const e=this.x-t.x,s=this.y-t.y;return Math.sqrt(e*e+s*s)}midPointFrom(t){return this.lerp(t)}min(t){return new ot(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new ot(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,e){return this.x=t,this.y=e,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){const e=this.x,s=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=s}clone(){return new ot(this.x,this.y)}rotate(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:at;const s=nt(t),i=rt(t),r=this.subtract(e);return new ot(r.x*i-r.y*s,r.x*s+r.y*i).add(e)}transform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new ot(t[0]*this.x+t[2]*this.y+(e?0:t[4]),t[1]*this.x+t[3]*this.y+(e?0:t[5]))}}const at=new ot(0,0),ht=t=>!!t&&Array.isArray(t._objects);function ct(t){class s extends t{constructor(){super(...arguments),e(this,"_objects",[])}
// ... rest of the minified code continues ...
