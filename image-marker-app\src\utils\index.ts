import * as XLSX from 'xlsx';
import { MarkerPoint, MarkerGroup, ExcelExportData, NamingRule } from '../types';

// 生成唯一ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 生成随机颜色
export const generateRandomColor = (): string => {
  const colors = [
    '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', '#bae637',
    '#52c41a', '#13c2c2', '#1890ff', '#2f54eb', '#722ed1',
    '#eb2f96', '#f759ab', '#fa541c', '#fa8c16', '#faad14'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

// 根据命名规则生成标记名称
export const generateMarkerName = (
  rule: NamingRule,
  existingMarkers: MarkerPoint[]
): string => {
  if (!rule.useAutoNumber) {
    return `${rule.prefix}${rule.suffix}`;
  }

  // 找到当前最大的编号
  const existingNumbers = existingMarkers
    .map(marker => {
      const match = marker.name.match(new RegExp(`${rule.prefix}(\\d+)${rule.suffix}`));
      return match ? parseInt(match[1], 10) : 0;
    })
    .filter(num => !isNaN(num));

  const maxNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : rule.startNumber - 1;
  const nextNumber = maxNumber + 1;
  const paddedNumber = nextNumber.toString().padStart(rule.numberPadding, '0');

  return `${rule.prefix}${paddedNumber}${rule.suffix}`;
};

// 导出数据到Excel
export const exportToExcel = (
  markers: MarkerPoint[],
  groups: MarkerGroup[],
  filename: string = '标记数据.xlsx'
): void => {
  const groupMap = new Map(groups.map(group => [group.id, group]));

  const exportData: ExcelExportData[] = markers.map(marker => {
    const group = groupMap.get(marker.groupId);
    return {
      标记名称: marker.name,
      X坐标: marker.x,
      Y坐标: marker.y,
      分组名称: group?.name || '未知分组',
      颜色: marker.color,
      是否可见: marker.visible ? '是' : '否',
      创建时间: marker.createdAt.toLocaleString('zh-CN'),
      更新时间: marker.updatedAt.toLocaleString('zh-CN')
    };
  });

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, '标记数据');

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 标记名称
    { wch: 10 }, // X坐标
    { wch: 10 }, // Y坐标
    { wch: 15 }, // 分组名称
    { wch: 10 }, // 颜色
    { wch: 10 }, // 是否可见
    { wch: 20 }, // 创建时间
    { wch: 20 }  // 更新时间
  ];
  worksheet['!cols'] = colWidths;

  XLSX.writeFile(workbook, filename);
};

// 从Excel导入数据
export const importFromExcel = (
  file: File,
  existingGroups: MarkerGroup[]
): Promise<{ markers: MarkerPoint[]; groups: MarkerGroup[] }> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as any[];

        const groupMap = new Map(existingGroups.map(group => [group.name, group]));
        const newGroups: MarkerGroup[] = [];
        const markers: MarkerPoint[] = [];

        jsonData.forEach((row, index) => {
          // 验证必要字段
          if (!row['标记名称'] || typeof row['X坐标'] !== 'number' || typeof row['Y坐标'] !== 'number') {
            console.warn(`第${index + 2}行数据不完整，已跳过`);
            return;
          }

          const groupName = row['分组名称'] || '默认分组';
          let group = groupMap.get(groupName);

          // 如果分组不存在，创建新分组
          if (!group) {
            group = {
              id: generateId(),
              name: groupName,
              color: generateRandomColor(),
              visible: true,
              createdAt: new Date()
            };
            groupMap.set(groupName, group);
            newGroups.push(group);
          }

          const marker: MarkerPoint = {
            id: generateId(),
            name: row['标记名称'],
            x: row['X坐标'],
            y: row['Y坐标'],
            groupId: group.id,
            color: row['颜色'] || group.color,
            visible: row['是否可见'] === '是' || row['是否可见'] === true,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          markers.push(marker);
        });

        resolve({ markers, groups: newGroups });
      } catch (error) {
        reject(new Error('Excel文件解析失败：' + (error as Error).message));
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    reader.readAsArrayBuffer(file);
  });
};

// 图片文件验证
export const validateImageFile = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  return allowedTypes.includes(file.type);
};

// 获取图片尺寸
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('无法获取图片尺寸'));
    };

    img.src = url;
  });
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 深拷贝对象
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
