/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 应用布局 */
.app-layout {
  height: 100vh;
  background: #f0f2f5;
}

.app-header {
  background: #001529;
  padding: 0 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.app-content {
  padding: 16px;
  height: calc(100vh - 64px);
  overflow: hidden;
}

/* 面板样式 */
.left-panel,
.canvas-panel,
.right-panel {
  height: 100%;
}

.left-panel .ant-row,
.left-panel .ant-col {
  height: 100%;
}

/* 画布区域优化 - 成为视觉焦点 */
.canvas-panel {
  position: relative;
  z-index: 1;
}

.canvas-panel .ant-card {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

/* 侧边面板优化 */
.left-panel,
.right-panel {
  z-index: 2;
}

.left-panel .ant-card,
.right-panel .ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

/* 紧凑型侧边面板样式 */
.left-panel .ant-card-body,
.right-panel .ant-card-body {
  padding: 12px;
}

.left-panel .ant-card-head,
.right-panel .ant-card-head {
  padding: 0 12px;
  min-height: 40px;
}

.left-panel .ant-card-head-title,
.right-panel .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .app-content {
    padding: 14px;
  }
}

@media (max-width: 1200px) {
  .app-content {
    padding: 12px;
  }

  /* 调整侧边面板间距 */
  .left-panel .ant-card-body,
  .right-panel .ant-card-body {
    padding: 10px;
  }
}

@media (max-width: 992px) {
  /* 平板端布局调整 */
  .left-panel,
  .canvas-panel,
  .right-panel {
    height: auto;
    margin-bottom: 12px;
  }

  .left-panel .ant-row {
    height: auto;
  }

  .left-panel .ant-col {
    height: auto;
    margin-bottom: 12px;
  }

  .app-content .ant-row {
    height: auto;
  }

  /* 平板端画布区域优化 */
  .canvas-panel {
    order: -1; /* 画布区域在移动端优先显示 */
    margin-bottom: 16px;
  }

  .canvas-panel .ant-card {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }

  .app-header .ant-typography {
    font-size: 18px !important;
  }

  .app-content {
    padding: 8px;
  }

  /* 移动端工具面板优化 */
  .left-panel .ant-card-body,
  .right-panel .ant-card-body {
    padding: 8px;
  }

  .left-panel .ant-card-head,
  .right-panel .ant-card-head {
    padding: 0 8px;
    min-height: 36px;
  }

  /* 移动端画布区域 */
  .canvas-panel .ant-card {
    min-height: 300px;
  }

  /* 确保标记工具可见性 */
  .left-panel .ant-col {
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 0 12px;
  }

  .app-header .ant-typography {
    font-size: 16px !important;
  }

  .app-content {
    padding: 6px;
  }

  /* 小屏幕设备优化 */
  .left-panel .ant-card-body,
  .right-panel .ant-card-body {
    padding: 6px;
  }

  .canvas-panel .ant-card {
    min-height: 250px;
  }

  /* 紧凑型按钮和控件 */
  .left-panel .ant-btn,
  .right-panel .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
  }

  .left-panel .ant-input,
  .right-panel .ant-input {
    font-size: 12px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design 组件样式覆盖 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-btn {
  border-radius: 6px;
}

.ant-input {
  border-radius: 6px;
}

.ant-select .ant-select-selector {
  border-radius: 6px;
}

.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-modal {
  border-radius: 8px;
  overflow: hidden;
}
