/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 应用布局 */
.app-layout {
  height: 100vh;
  background: #f0f2f5;
}

.app-header {
  background: #001529;
  padding: 0 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.app-content {
  padding: 16px;
  height: calc(100vh - 64px);
  overflow: hidden;
}

/* 面板样式 */
.left-panel,
.canvas-panel,
.right-panel {
  height: 100%;
}

.left-panel .ant-row,
.left-panel .ant-col {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .app-content {
    padding: 12px;
  }
}

@media (max-width: 992px) {
  .left-panel,
  .canvas-panel,
  .right-panel {
    height: auto;
    margin-bottom: 16px;
  }

  .left-panel .ant-row {
    height: auto;
  }

  .left-panel .ant-col {
    height: auto;
    margin-bottom: 16px;
  }

  .app-content .ant-row {
    height: auto;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }

  .app-header .ant-typography {
    font-size: 18px !important;
  }

  .app-content {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 0 12px;
  }

  .app-header .ant-typography {
    font-size: 16px !important;
  }

  .app-content {
    padding: 4px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design 组件样式覆盖 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-btn {
  border-radius: 6px;
}

.ant-input {
  border-radius: 6px;
}

.ant-select .ant-select-selector {
  border-radius: 6px;
}

.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-modal {
  border-radius: 8px;
  overflow: hidden;
}
