import React, { useState, useRef } from 'react';
import {
  Card,
  Button,
  Space,
  Upload,
  message,
  Typography,
  Divider,
  Modal,
  Progress,
  Alert
} from 'antd';
import {
  DownloadOutlined,
  UploadOutlined,
  FileExcelOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { useApp } from '../../context/AppContext';
import { exportToExcel, importFromExcel } from '../../utils';
import './index.css';

const { Title, Text } = Typography;

const DataExporter: React.FC = () => {
  const { state, dispatch } = useApp();
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 导出数据到Excel
  const handleExport = () => {
    if (state.markers.length === 0) {
      message.warning('没有标记数据可以导出');
      return;
    }

    try {
      const filename = `标记数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      exportToExcel(state.markers, state.groups, filename);
      message.success('数据导出成功！');
    } catch (error) {
      message.error('导出失败：' + (error as Error).message);
    }
  };

  // 处理文件导入
  const handleImport = async (file: File) => {
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      message.error('请选择Excel文件（.xlsx或.xls格式）');
      return false;
    }

    setImporting(true);
    setImportProgress(0);
    setImportModalVisible(true);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setImportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const result = await importFromExcel(file, state.groups);
      
      clearInterval(progressInterval);
      setImportProgress(100);

      // 延迟一下让用户看到100%
      setTimeout(() => {
        dispatch({ type: 'IMPORT_DATA', payload: result });
        message.success(`成功导入 ${result.markers.length} 个标记和 ${result.groups.length} 个新分组`);
        setImportModalVisible(false);
        setImporting(false);
        setImportProgress(0);
      }, 500);

    } catch (error) {
      message.error('导入失败：' + (error as Error).message);
      setImportModalVisible(false);
      setImporting(false);
      setImportProgress(0);
    }

    return false; // 阻止默认上传行为
  };

  // 上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls',
    beforeUpload: handleImport,
    showUploadList: false,
    disabled: importing
  };

  // 手动选择文件
  const handleManualImport = () => {
    fileInputRef.current?.click();
  };

  // 文件输入变化处理
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImport(file);
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    const templateData = [
      {
        标记名称: '示例标记1',
        X坐标: 100,
        Y坐标: 150,
        分组名称: '默认分组',
        颜色: '#1890ff',
        是否可见: '是',
        创建时间: new Date().toLocaleString('zh-CN'),
        更新时间: new Date().toLocaleString('zh-CN')
      },
      {
        标记名称: '示例标记2',
        X坐标: 200,
        Y坐标: 250,
        分组名称: '新分组',
        颜色: '#52c41a',
        是否可见: '是',
        创建时间: new Date().toLocaleString('zh-CN'),
        更新时间: new Date().toLocaleString('zh-CN')
      }
    ];

    try {
      exportToExcel([], [], '标记数据导入模板.xlsx');
      message.success('模板下载成功！');
    } catch (error) {
      message.error('模板下载失败：' + (error as Error).message);
    }
  };

  return (
    <Card className="data-exporter">
      <Title level={5} style={{ marginBottom: 16 }}>
        数据导入导出
      </Title>

      <div className="export-section">
        <div className="section-header">
          <FileExcelOutlined style={{ color: '#52c41a', marginRight: 8 }} />
          <Text strong>导出数据</Text>
        </div>
        <div className="section-content">
          <Text type="secondary" style={{ display: 'block', marginBottom: 12 }}>
            将当前所有标记数据导出为Excel文件
          </Text>
          <Space>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleExport}
              disabled={state.markers.length === 0}
            >
              导出Excel ({state.markers.length} 个标记)
            </Button>
            <Text type="secondary">
              包含标记坐标、分组信息等完整数据
            </Text>
          </Space>
        </div>
      </div>

      <Divider />

      <div className="import-section">
        <div className="section-header">
          <UploadOutlined style={{ color: '#1890ff', marginRight: 8 }} />
          <Text strong>导入数据</Text>
        </div>
        <div className="section-content">
          <Text type="secondary" style={{ display: 'block', marginBottom: 12 }}>
            从Excel文件导入标记数据，支持批量添加标记和分组
          </Text>
          
          <Alert
            message="导入说明"
            description={
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                <li>Excel文件必须包含：标记名称、X坐标、Y坐标列</li>
                <li>可选列：分组名称、颜色、是否可见</li>
                <li>如果分组不存在，系统会自动创建新分组</li>
                <li>导入的数据会添加到现有数据中，不会覆盖</li>
              </ul>
            }
            type="info"
            icon={<InfoCircleOutlined />}
            style={{ marginBottom: 16 }}
          />

          <Space direction="vertical" style={{ width: '100%' }}>
            <Space>
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />} loading={importing}>
                  选择Excel文件导入
                </Button>
              </Upload>
              <Button
                type="link"
                onClick={handleDownloadTemplate}
                size="small"
              >
                下载导入模板
              </Button>
            </Space>
            
            <Text type="secondary" style={{ fontSize: 12 }}>
              支持 .xlsx 和 .xls 格式文件
            </Text>
          </Space>

          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx,.xls"
            style={{ display: 'none' }}
            onChange={handleFileInputChange}
          />
        </div>
      </div>

      <Modal
        title="导入进度"
        open={importModalVisible}
        footer={null}
        closable={false}
        centered
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress
            type="circle"
            percent={importProgress}
            status={importing ? 'active' : 'success'}
          />
          <div style={{ marginTop: 16 }}>
            <Text>{importing ? '正在解析Excel文件...' : '导入完成！'}</Text>
          </div>
        </div>
      </Modal>
    </Card>
  );
};

export default DataExporter;
