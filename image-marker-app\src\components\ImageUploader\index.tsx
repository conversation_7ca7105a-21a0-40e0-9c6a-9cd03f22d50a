import React, { useState, useRef } from 'react';
import { Upload, Button, message, Card, Typography, Space } from 'antd';
import { InboxOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { useApp } from '../../context/AppContext';
import { validateImageFile, getImageDimensions, formatFileSize, generateId } from '../../utils';
import type { ImageInfo } from '../../types';
import './index.css';

const { Dragger } = Upload;
const { Text, Title } = Typography;

const ImageUploader: React.FC = () => {
  const { state, dispatch } = useApp();
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!validateImageFile(file)) {
      message.error('请选择有效的图片文件（JPG、PNG、GIF、WebP）');
      return false;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB限制
      message.error('图片文件大小不能超过10MB');
      return false;
    }

    setUploading(true);
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const dimensions = await getImageDimensions(file);
      const url = URL.createObjectURL(file);
      
      const imageInfo: ImageInfo = {
        id: generateId(),
        name: file.name,
        url,
        width: dimensions.width,
        height: dimensions.height,
        file
      };

      dispatch({ type: 'SET_IMAGE', payload: imageInfo });
      message.success('图片上传成功！');
    } catch (error) {
      message.error('图片处理失败：' + (error as Error).message);
    } finally {
      setUploading(false);
      dispatch({ type: 'SET_LOADING', payload: false });
    }

    return false; // 阻止默认上传行为
  };

  // 上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: 'image/*',
    beforeUpload: handleFileUpload,
    showUploadList: false,
    disabled: uploading
  };

  // 清除当前图片
  const handleClearImage = () => {
    if (state.currentImage) {
      URL.revokeObjectURL(state.currentImage.url);
      dispatch({ type: 'CLEAR_ALL' });
      message.success('已清除当前图片');
    }
  };

  // 手动选择文件
  const handleManualUpload = () => {
    fileInputRef.current?.click();
  };

  // 文件输入变化处理
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  // 加载示例图片
  const handleLoadDemo = async () => {
    setUploading(true);
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // 创建一个简单的示例图片 (SVG)
      const svgContent = `
        <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#e0e0e0" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          <rect x="100" y="100" width="600" height="400" fill="#f8f9fa" stroke="#1890ff" stroke-width="2" rx="10"/>
          <text x="400" y="200" text-anchor="middle" font-family="Arial" font-size="24" fill="#666">
            示例图片 - 点击任意位置添加标记
          </text>
          <text x="400" y="240" text-anchor="middle" font-family="Arial" font-size="16" fill="#999">
            这是一个演示图片，您可以在上面练习添加标记点
          </text>
          <circle cx="200" cy="350" r="8" fill="#52c41a" opacity="0.7"/>
          <text x="220" y="355" font-family="Arial" font-size="12" fill="#52c41a">示例标记点</text>
          <circle cx="600" cy="350" r="8" fill="#ff4d4f" opacity="0.7"/>
          <text x="520" y="355" font-family="Arial" font-size="12" fill="#ff4d4f">另一个示例</text>
        </svg>
      `;

      // 将SVG转换为Blob
      const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
      const file = new File([svgBlob], '示例图片.svg', { type: 'image/svg+xml' });

      const url = URL.createObjectURL(file);

      const imageInfo: ImageInfo = {
        id: generateId(),
        name: '示例图片.svg',
        url,
        width: 800,
        height: 600,
        file
      };

      dispatch({ type: 'SET_IMAGE', payload: imageInfo });
      message.success('示例图片加载成功！现在您可以点击图片添加标记点了');
    } catch (error) {
      message.error('示例图片加载失败：' + (error as Error).message);
    } finally {
      setUploading(false);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  return (
    <div className="image-uploader">
      {!state.currentImage ? (
        <Card className="upload-card" title="📷 上传图片">
          <Dragger {...uploadProps} className="upload-dragger">
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              拖拽图片到此处或点击上传
            </p>
            <p className="ant-upload-hint">
              支持 JPG、PNG、GIF、WebP 格式
            </p>
            <p className="ant-upload-hint">
              文件大小不超过 10MB
            </p>
          </Dragger>

          <div className="upload-divider">
            <Text type="secondary">或者</Text>
          </div>

          <div className="upload-button-container">
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={handleManualUpload}
              loading={uploading}
              size="small"
              block
            >
              {uploading ? '上传中...' : '选择图片文件'}
            </Button>
          </div>

          <div className="upload-tips">
            <Text type="secondary" className="tip-text">
              💡 上传后即可开始在图片上添加标记点
            </Text>
          </div>

          <div className="demo-section">
            <Button
              type="link"
              size="small"
              onClick={handleLoadDemo}
              className="demo-button"
            >
              🎯 加载示例图片试用
            </Button>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            style={{ display: 'none' }}
            onChange={handleFileInputChange}
          />
        </Card>
      ) : (
        <Card className="image-info-card">
          <div className="image-info-header">
            <Title level={5} style={{ margin: 0 }}>
              当前图片信息
            </Title>
            <Space>
              <Button 
                type="primary" 
                icon={<UploadOutlined />}
                onClick={handleManualUpload}
                size="small"
              >
                更换图片
              </Button>
              <Button 
                danger 
                icon={<DeleteOutlined />}
                onClick={handleClearImage}
                size="small"
              >
                清除图片
              </Button>
            </Space>
          </div>

          <div className="image-info-content">
            <div className="image-info-item">
              <Text strong>文件名：</Text>
              <Text>{state.currentImage.name}</Text>
            </div>
            <div className="image-info-item">
              <Text strong>尺寸：</Text>
              <Text>{state.currentImage.width} × {state.currentImage.height} 像素</Text>
            </div>
            <div className="image-info-item">
              <Text strong>文件大小：</Text>
              <Text>{formatFileSize(state.currentImage.file.size)}</Text>
            </div>
            <div className="image-info-item">
              <Text strong>标记数量：</Text>
              <Text>{state.markers.length} 个</Text>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            style={{ display: 'none' }}
            onChange={handleFileInputChange}
          />
        </Card>
      )}
    </div>
  );
};

export default ImageUploader;
