import React, { useState, useRef } from 'react';
import { Upload, Button, message, Card, Typography, Space } from 'antd';
import { InboxOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { useApp } from '../../context/AppContext';
import { validateImageFile, getImageDimensions, formatFileSize, generateId } from '../../utils';
import type { ImageInfo } from '../../types';
import './index.css';

const { Dragger } = Upload;
const { Text, Title } = Typography;

const ImageUploader: React.FC = () => {
  const { state, dispatch } = useApp();
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!validateImageFile(file)) {
      message.error('请选择有效的图片文件（JPG、PNG、GIF、WebP）');
      return false;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB限制
      message.error('图片文件大小不能超过10MB');
      return false;
    }

    setUploading(true);
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const dimensions = await getImageDimensions(file);
      const url = URL.createObjectURL(file);
      
      const imageInfo: ImageInfo = {
        id: generateId(),
        name: file.name,
        url,
        width: dimensions.width,
        height: dimensions.height,
        file
      };

      dispatch({ type: 'SET_IMAGE', payload: imageInfo });
      message.success('图片上传成功！');
    } catch (error) {
      message.error('图片处理失败：' + (error as Error).message);
    } finally {
      setUploading(false);
      dispatch({ type: 'SET_LOADING', payload: false });
    }

    return false; // 阻止默认上传行为
  };

  // 上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: 'image/*',
    beforeUpload: handleFileUpload,
    showUploadList: false,
    disabled: uploading
  };

  // 清除当前图片
  const handleClearImage = () => {
    if (state.currentImage) {
      URL.revokeObjectURL(state.currentImage.url);
      dispatch({ type: 'CLEAR_ALL' });
      message.success('已清除当前图片');
    }
  };

  // 手动选择文件
  const handleManualUpload = () => {
    fileInputRef.current?.click();
  };

  // 文件输入变化处理
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  return (
    <div className="image-uploader">
      {!state.currentImage ? (
        <Card className="upload-card">
          <Title level={4} style={{ textAlign: 'center', marginBottom: 24 }}>
            上传图片
          </Title>
          
          <Dragger {...uploadProps} className="upload-dragger">
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              点击或拖拽图片文件到此区域上传
            </p>
            <p className="ant-upload-hint">
              支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 10MB
            </p>
          </Dragger>

          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Text type="secondary">或者</Text>
          </div>

          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Button 
              type="primary" 
              icon={<UploadOutlined />}
              onClick={handleManualUpload}
              loading={uploading}
            >
              选择图片文件
            </Button>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            style={{ display: 'none' }}
            onChange={handleFileInputChange}
          />
        </Card>
      ) : (
        <Card className="image-info-card">
          <div className="image-info-header">
            <Title level={5} style={{ margin: 0 }}>
              当前图片信息
            </Title>
            <Space>
              <Button 
                type="primary" 
                icon={<UploadOutlined />}
                onClick={handleManualUpload}
                size="small"
              >
                更换图片
              </Button>
              <Button 
                danger 
                icon={<DeleteOutlined />}
                onClick={handleClearImage}
                size="small"
              >
                清除图片
              </Button>
            </Space>
          </div>

          <div className="image-info-content">
            <div className="image-info-item">
              <Text strong>文件名：</Text>
              <Text>{state.currentImage.name}</Text>
            </div>
            <div className="image-info-item">
              <Text strong>尺寸：</Text>
              <Text>{state.currentImage.width} × {state.currentImage.height} 像素</Text>
            </div>
            <div className="image-info-item">
              <Text strong>文件大小：</Text>
              <Text>{formatFileSize(state.currentImage.file.size)}</Text>
            </div>
            <div className="image-info-item">
              <Text strong>标记数量：</Text>
              <Text>{state.markers.length} 个</Text>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            style={{ display: 'none' }}
            onChange={handleFileInputChange}
          />
        </Card>
      )}
    </div>
  );
};

export default ImageUploader;
