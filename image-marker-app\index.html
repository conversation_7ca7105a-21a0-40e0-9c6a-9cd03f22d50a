<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>图片标记管理系统</title>

    <!-- Fabric.js v6.6.6 CDN加载 -->
    <script>
      // 加载Fabric.js的函数
      function loadFabricJS() {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.onload = () => {
            if (typeof fabric !== 'undefined') {
              console.log('✅ Fabric.js loaded successfully from primary CDN');
              console.log('Fabric.js version:', fabric.version);
              resolve();
            } else {
              reject(new Error('Fabric.js object not found'));
            }
          };
          script.onerror = () => reject(new Error('Failed to load from primary CDN'));
          script.src = 'https://unpkg.com/fabric@6.6.6/dist/index.min.js';
          document.head.appendChild(script);
        });
      }

      // 备用CDN
      function loadFabricJSFallback() {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.onload = () => {
            if (typeof fabric !== 'undefined') {
              console.log('✅ Fabric.js loaded successfully from fallback CDN');
              console.log('Fabric.js version:', fabric.version);
              resolve();
            } else {
              reject(new Error('Fabric.js object not found'));
            }
          };
          script.onerror = () => reject(new Error('Failed to load from fallback CDN'));
          script.src = 'https://cdn.jsdelivr.net/npm/fabric@6.6.6/dist/index.min.js';
          document.head.appendChild(script);
        });
      }

      // 页面加载时自动加载Fabric.js
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🔄 开始加载Fabric.js...');

        loadFabricJS().catch(() => {
          console.warn('⚠️ 主CDN失败，尝试备用CDN...');
          return loadFabricJSFallback();
        }).then(() => {
          console.log('🎉 Fabric.js加载完成，React应用可以开始使用');
          window.fabricReady = true;
          // 触发自定义事件通知React应用
          window.dispatchEvent(new CustomEvent('fabricReady'));
        }).catch((error) => {
          console.error('❌ 所有CDN都失败:', error);
          window.fabricError = true;
          window.dispatchEvent(new CustomEvent('fabricError', { detail: error }));
        });
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
