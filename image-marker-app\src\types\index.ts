// 标记点类型定义
export interface MarkerPoint {
  id: string;
  x: number;
  y: number;
  name: string;
  groupId: string;
  visible: boolean;
  color: string;
  createdAt: Date;
  updatedAt: Date;
}

// 标记组类型定义
export interface MarkerGroup {
  id: string;
  name: string;
  color: string;
  visible: boolean;
  description?: string;
  createdAt: Date;
}

// 图片信息类型定义
export interface ImageInfo {
  id: string;
  name: string;
  url: string;
  width: number;
  height: number;
  file: File;
}

// 应用状态类型定义
export interface AppState {
  currentImage: ImageInfo | null;
  markers: MarkerPoint[];
  groups: MarkerGroup[];
  selectedMarkers: string[];
  selectedGroup: string | null;
  isLoading: boolean;
  error: string | null;
}

// 动作类型定义
export type AppAction =
  | { type: 'SET_IMAGE'; payload: ImageInfo }
  | { type: 'ADD_MARKER'; payload: MarkerPoint }
  | { type: 'UPDATE_MARKER'; payload: { id: string; updates: Partial<MarkerPoint> } }
  | { type: 'DELETE_MARKER'; payload: string }
  | { type: 'DELETE_MARKERS'; payload: string[] }
  | { type: 'ADD_GROUP'; payload: MarkerGroup }
  | { type: 'UPDATE_GROUP'; payload: { id: string; updates: Partial<MarkerGroup> } }
  | { type: 'DELETE_GROUP'; payload: string }
  | { type: 'SELECT_MARKERS'; payload: string[] }
  | { type: 'SELECT_GROUP'; payload: string | null }
  | { type: 'TOGGLE_MARKER_VISIBILITY'; payload: string }
  | { type: 'TOGGLE_GROUP_VISIBILITY'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'IMPORT_DATA'; payload: { markers: MarkerPoint[]; groups: MarkerGroup[] } }
  | { type: 'CLEAR_ALL' };

// Excel导出数据格式
export interface ExcelExportData {
  标记名称: string;
  X坐标: number;
  Y坐标: number;
  分组名称: string;
  颜色: string;
  是否可见: string;
  创建时间: string;
  更新时间: string;
}

// 命名规则配置
export interface NamingRule {
  prefix: string;
  suffix: string;
  useAutoNumber: boolean;
  startNumber: number;
  numberPadding: number;
}
