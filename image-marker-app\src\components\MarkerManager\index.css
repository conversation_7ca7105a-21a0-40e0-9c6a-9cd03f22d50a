.marker-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.marker-manager .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.marker-manager .ant-table-wrapper {
  flex: 1;
}

.marker-manager .ant-table {
  font-size: 12px;
}

.marker-manager .ant-table-thead > tr > th {
  padding: 8px;
  font-weight: 600;
  background: #fafafa;
}

.marker-manager .ant-table-tbody > tr > td {
  padding: 8px;
}

.marker-manager .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.marker-manager .ant-table-tbody > tr.ant-table-row-selected > td {
  background: #e6f7ff;
}

.marker-manager .ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background: #bae7ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .manager-header .ant-space {
    justify-content: center;
  }
  
  .marker-manager .ant-table {
    font-size: 11px;
  }
  
  .marker-manager .ant-table-thead > tr > th,
  .marker-manager .ant-table-tbody > tr > td {
    padding: 6px 4px;
  }
}

@media (max-width: 480px) {
  .marker-manager .ant-card-body {
    padding: 12px;
  }
}
