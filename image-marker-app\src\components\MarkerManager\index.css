.marker-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.marker-manager .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.manager-header .ant-typography {
  font-size: 13px;
  font-weight: 600;
  margin: 0;
}

.manager-header .ant-btn {
  font-size: 11px;
  height: 26px;
  padding: 2px 6px;
}

.marker-manager .ant-table-wrapper {
  flex: 1;
}

.marker-manager .ant-table {
  font-size: 11px;
}

.marker-manager .ant-table-thead > tr > th {
  padding: 6px 4px;
  font-weight: 600;
  background: #fafafa;
  font-size: 11px;
}

.marker-manager .ant-table-tbody > tr > td {
  padding: 6px 4px;
  font-size: 11px;
}

.marker-manager .ant-table-tbody > tr > td .ant-btn {
  font-size: 10px;
  padding: 2px 4px;
  height: 22px;
}

.marker-manager .ant-table-tbody > tr > td .ant-tag {
  font-size: 10px;
  padding: 1px 4px;
  margin: 0;
}

.marker-manager .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.marker-manager .ant-table-tbody > tr.ant-table-row-selected > td {
  background: #e6f7ff;
}

.marker-manager .ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background: #bae7ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .manager-header .ant-space {
    justify-content: center;
  }
  
  .marker-manager .ant-table {
    font-size: 11px;
  }
  
  .marker-manager .ant-table-thead > tr > th,
  .marker-manager .ant-table-tbody > tr > td {
    padding: 6px 4px;
  }
}

@media (max-width: 480px) {
  .marker-manager .ant-card-body {
    padding: 12px;
  }
}
