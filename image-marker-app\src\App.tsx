import React from 'react';
import { Layout, Row, Col, Typography, ConfigProvider } from 'antd';
import { AppProvider } from './context/AppContext';
import ImageUploader from './components/ImageUploader';
import ImageCanvas from './components/ImageCanvas';
import MarkerManager from './components/MarkerManager';
import GroupManager from './components/GroupManager';
import DataExporter from './components/DataExporter';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

const { Header, Content } = Layout;
const { Title } = Typography;

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AppProvider>
        <Layout className="app-layout">
          <Header className="app-header">
            <Title level={2} style={{ color: 'white', margin: 0 }}>
              图片标记管理系统
            </Title>
          </Header>

          <Content className="app-content">
            <Row gutter={[16, 16]} style={{ height: '100%' }}>
              {/* 左侧面板 */}
              <Col xs={24} lg={8} className="left-panel">
                <Row gutter={[0, 16]} style={{ height: '100%' }}>
                  <Col span={24} style={{ height: '40%' }}>
                    <ImageUploader />
                  </Col>
                  <Col span={24} style={{ height: '35%' }}>
                    <GroupManager />
                  </Col>
                  <Col span={24} style={{ height: '25%' }}>
                    <DataExporter />
                  </Col>
                </Row>
              </Col>

              {/* 中间画布区域 */}
              <Col xs={24} lg={10} className="canvas-panel">
                <ImageCanvas />
              </Col>

              {/* 右侧标记管理 */}
              <Col xs={24} lg={6} className="right-panel">
                <MarkerManager />
              </Col>
            </Row>
          </Content>
        </Layout>
      </AppProvider>
    </ConfigProvider>
  );
}

export default App;
