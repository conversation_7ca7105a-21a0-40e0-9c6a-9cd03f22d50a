const fs = require('fs');
const path = require('path');

// 读取构建后的JavaScript文件
const jsFilePath = path.join(__dirname, 'dist/assets/index-BCn2mr22.js');

try {
  console.log('🔧 开始修复Fabric.js API调用...');
  
  let content = fs.readFileSync(jsFilePath, 'utf8');
  
  // 替换FabricImage.fromURL为Image.fromURL
  const originalContent = content;
  content = content.replace(/fabric\.FabricImage\.fromURL/g, 'fabric.Image.fromURL');
  
  if (content !== originalContent) {
    fs.writeFileSync(jsFilePath, content, 'utf8');
    console.log('✅ 成功修复Fabric.js API调用');
    console.log('📝 已将 fabric.FabricImage.fromURL 替换为 fabric.Image.fromURL');
  } else {
    console.log('ℹ️ 未找到需要修复的API调用');
  }
  
} catch (error) {
  console.error('❌ 修复失败:', error.message);
}
