.image-uploader {
  width: 100%;
  height: 100%;
}

.upload-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-card .ant-card-head {
  padding: 8px 16px;
  min-height: 40px;
}

.upload-card .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
}

.upload-card .ant-card-body {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.upload-dragger {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-dragger:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-dragger.ant-upload-drag-hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.image-info-card {
  height: 100%;
}

.image-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.image-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.image-info-item .ant-typography {
  margin: 0;
}

/* 紧凑型上传区域 */
.upload-dragger .ant-upload-text {
  font-size: 14px;
  margin: 8px 0 4px;
}

.upload-dragger .ant-upload-hint {
  font-size: 11px;
  margin: 2px 0;
  color: #999;
}

.upload-dragger .ant-upload-drag-icon {
  margin-bottom: 8px;
}

.upload-dragger .ant-upload-drag-icon .anticon {
  font-size: 36px;
  color: #1890ff;
}

/* 上传区域新增样式 */
.upload-divider {
  text-align: center;
  margin: 12px 0 8px;
}

.upload-button-container {
  margin-bottom: 12px;
}

.upload-tips {
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #52c41a;
}

.upload-tips .ant-typography {
  margin: 0;
}

.tip-text {
  font-size: 11px;
}

/* 示例图片区域 */
.demo-section {
  text-align: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.demo-button {
  font-size: 11px;
  padding: 2px 8px;
  height: auto;
  color: #1890ff;
}

.demo-button:hover {
  color: #40a9ff;
  background: #f0f8ff;
}

/* 隐藏的文件输入 */
input[type="file"] {
  position: absolute;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}

/* 图片信息紧凑显示 */
.image-info-card .ant-card-body {
  padding: 12px;
}

.image-info-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
}

.image-info-content {
  gap: 6px;
}

.image-info-item {
  padding: 2px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .upload-card .ant-card-body {
    padding: 10px;
  }

  .upload-dragger .ant-upload-drag-icon .anticon {
    font-size: 32px;
  }

  .upload-dragger .ant-upload-text {
    font-size: 13px;
  }

  .upload-dragger .ant-upload-hint {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .image-info-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .image-info-header .ant-space {
    justify-content: center;
  }

  .image-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .upload-card .ant-card-body {
    padding: 8px;
  }

  .upload-dragger .ant-upload-drag-icon .anticon {
    font-size: 28px;
  }
}
