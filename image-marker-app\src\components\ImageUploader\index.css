.image-uploader {
  width: 100%;
  height: 100%;
}

.upload-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-dragger {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-dragger:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-dragger.ant-upload-drag-hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.image-info-card {
  height: 100%;
}

.image-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.image-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.image-info-item .ant-typography {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-info-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .image-info-header .ant-space {
    justify-content: center;
  }
  
  .image-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
