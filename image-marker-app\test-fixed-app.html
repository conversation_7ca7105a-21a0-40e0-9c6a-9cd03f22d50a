<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的图片标记应用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .canvas-container {
            border: 2px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            background: #fafafa;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        canvas {
            border: 1px solid #ccc;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .log {
            background: #f6f6f6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .warning { background: #fffbe6; border: 1px solid #ffe58f; color: #faad14; }
        .loading { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复后的图片标记应用测试</h1>
        
        <div class="test-section">
            <h3>📦 Fabric.js 加载状态</h3>
            <div id="fabric-status" class="status loading">正在加载Fabric.js...</div>
        </div>

        <div class="test-section">
            <h3>🎨 图片标记测试</h3>
            <div class="canvas-container">
                <canvas id="main-canvas" width="600" height="400"></canvas>
            </div>
            <br>
            <button id="btn-load-example" onclick="loadExampleImage()" disabled>加载示例图片</button>
            <button id="btn-add-marker" onclick="addTestMarker()" disabled>添加测试标记</button>
            <button id="btn-clear" onclick="clearCanvas()" disabled>清空画布</button>
            <input type="file" id="file-input" accept="image/*" onchange="loadUserImage(this)" disabled>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <!-- Fabric.js CDN加载 -->
    <script>
        let fabricCanvas = null;
        let fabricReady = false;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'loading') {
            const statusElement = document.getElementById('fabric-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function enableButtons() {
            document.getElementById('btn-load-example').disabled = false;
            document.getElementById('btn-add-marker').disabled = false;
            document.getElementById('btn-clear').disabled = false;
            document.getElementById('file-input').disabled = false;
        }

        // 加载Fabric.js的函数
        function loadFabricJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        log('✅ Fabric.js loaded successfully from primary CDN');
                        log(`Fabric.js version: ${fabric.version}`);
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from primary CDN'));
                script.src = 'https://unpkg.com/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        // 备用CDN
        function loadFabricJSFallback() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        log('✅ Fabric.js loaded successfully from fallback CDN');
                        log(`Fabric.js version: ${fabric.version}`);
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from fallback CDN'));
                script.src = 'https://cdn.jsdelivr.net/npm/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        function initializeCanvas() {
            try {
                const canvasElement = document.getElementById('main-canvas');
                fabricCanvas = new fabric.Canvas(canvasElement, {
                    selection: true,
                    backgroundColor: '#f5f5f5'
                });
                
                log('✅ Canvas初始化成功');
                log(`Canvas尺寸: ${fabricCanvas.width}x${fabricCanvas.height}`);
                
                // 添加点击事件监听
                fabricCanvas.on('mouse:down', function(e) {
                    if (!e.target) {
                        const pointer = fabricCanvas.getPointer(e.e);
                        log(`Canvas点击位置: (${Math.round(pointer.x)}, ${Math.round(pointer.y)})`);
                    }
                });
                
                enableButtons();
                updateStatus('✅ Fabric.js加载完成，Canvas已初始化', 'success');
                
            } catch (error) {
                log(`❌ Canvas初始化失败: ${error.message}`);
                updateStatus('❌ Canvas初始化失败', 'error');
            }
        }

        function loadExampleImage() {
            if (!fabricCanvas) {
                log('❌ Canvas未初始化');
                return;
            }

            try {
                log('开始加载示例图片...');
                
                // 创建示例SVG
                const svgContent = `
                    <svg width="500" height="300" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                        <rect x="50" y="50" width="400" height="200" fill="#f8f9fa" stroke="#1890ff" stroke-width="2" rx="10"/>
                        <text x="250" y="120" text-anchor="middle" font-family="Arial" font-size="18" fill="#666">
                            示例图片 - 图片标记测试
                        </text>
                        <text x="250" y="150" text-anchor="middle" font-family="Arial" font-size="14" fill="#999">
                            点击图片任意位置添加标记点
                        </text>
                        <circle cx="150" cy="200" r="6" fill="#52c41a" opacity="0.7"/>
                        <text x="170" y="205" font-family="Arial" font-size="12" fill="#52c41a">示例标记</text>
                    </svg>
                `;

                const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);

                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(`✅ 示例图片加载成功: ${img.width}x${img.height}`);
                    
                    fabricCanvas.clear();
                    
                    // 计算缩放
                    const scale = Math.min(
                        (fabricCanvas.width - 40) / img.width,
                        (fabricCanvas.height - 40) / img.height,
                        1
                    );
                    
                    img.set({
                        left: (fabricCanvas.width - img.width * scale) / 2,
                        top: (fabricCanvas.height - img.height * scale) / 2,
                        scaleX: scale,
                        scaleY: scale,
                        selectable: false,
                        evented: false
                    });
                    
                    fabricCanvas.add(img);
                    fabricCanvas.sendObjectToBack(img);
                    fabricCanvas.renderAll();
                    
                    log('✅ 示例图片添加到Canvas成功');
                    URL.revokeObjectURL(url);
                    
                }).catch((error) => {
                    log(`❌ 示例图片加载失败: ${error.message}`);
                    URL.revokeObjectURL(url);
                });
                
            } catch (error) {
                log(`❌ 示例图片测试失败: ${error.message}`);
            }
        }

        function addTestMarker() {
            if (!fabricCanvas) {
                log('❌ Canvas未初始化');
                return;
            }

            try {
                const x = 100 + Math.random() * 400;
                const y = 100 + Math.random() * 200;
                
                const circle = new fabric.Circle({
                    left: x - 5,
                    top: y - 5,
                    radius: 5,
                    fill: '#ff4d4f',
                    stroke: '#ffffff',
                    strokeWidth: 2,
                    selectable: true
                });

                const text = new fabric.Text(`标记${fabricCanvas.getObjects().length + 1}`, {
                    left: x + 8,
                    top: y - 8,
                    fontSize: 12,
                    fill: '#ff4d4f',
                    backgroundColor: 'rgba(255, 255, 255, 0.8)'
                });

                fabricCanvas.add(circle);
                fabricCanvas.add(text);
                fabricCanvas.renderAll();

                log(`✅ 添加标记点成功: (${Math.round(x)}, ${Math.round(y)})`);
                
            } catch (error) {
                log(`❌ 添加标记点失败: ${error.message}`);
            }
        }

        function clearCanvas() {
            if (!fabricCanvas) {
                log('❌ Canvas未初始化');
                return;
            }

            try {
                fabricCanvas.clear();
                fabricCanvas.backgroundColor = '#f5f5f5';
                fabricCanvas.renderAll();
                log('✅ Canvas已清空');
            } catch (error) {
                log(`❌ 清空Canvas失败: ${error.message}`);
            }
        }

        function loadUserImage(input) {
            const file = input.files[0];
            if (!file || !fabricCanvas) return;

            try {
                log(`开始加载用户图片: ${file.name} (${file.size} bytes)`);
                
                const url = URL.createObjectURL(file);
                
                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(`✅ 用户图片加载成功: ${img.width}x${img.height}`);
                    
                    fabricCanvas.clear();
                    
                    // 计算缩放
                    const scale = Math.min(
                        (fabricCanvas.width - 40) / img.width,
                        (fabricCanvas.height - 40) / img.height,
                        1
                    );
                    
                    img.set({
                        left: (fabricCanvas.width - img.width * scale) / 2,
                        top: (fabricCanvas.height - img.height * scale) / 2,
                        scaleX: scale,
                        scaleY: scale,
                        selectable: false,
                        evented: false
                    });
                    
                    fabricCanvas.add(img);
                    fabricCanvas.sendObjectToBack(img);
                    fabricCanvas.renderAll();
                    
                    log('✅ 用户图片添加到Canvas成功');
                    URL.revokeObjectURL(url);
                    
                }).catch((error) => {
                    log(`❌ 用户图片加载失败: ${error.message}`);
                    URL.revokeObjectURL(url);
                });
                
            } catch (error) {
                log(`❌ 用户图片测试失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动加载Fabric.js
        window.onload = function() {
            log('页面加载完成，开始加载Fabric.js...');
            updateStatus('正在加载Fabric.js...', 'loading');
            
            loadFabricJS().catch(() => {
                log('主CDN失败，尝试备用CDN...');
                updateStatus('主CDN失败，尝试备用CDN...', 'warning');
                return loadFabricJSFallback();
            }).then(() => {
                log('✅ Fabric.js加载完成');
                fabricReady = true;
                initializeCanvas();
            }).catch((error) => {
                log('❌ 所有CDN都失败');
                updateStatus('❌ Fabric.js加载失败', 'error');
            });
        };
    </script>
</body>
</html>
