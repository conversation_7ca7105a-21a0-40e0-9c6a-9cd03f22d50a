// 测试Fabric.js v6导入和基本功能
import { Canvas, FabricImage, Circle, Text, Group } from 'fabric';

console.log('Fabric.js 导入测试:');
console.log('Canvas:', Canvas);
console.log('FabricImage:', FabricImage);
console.log('Circle:', Circle);
console.log('Text:', Text);
console.log('Group:', Group);

// 测试基本功能
export const testFabricImports = () => {
  try {
    // 测试Canvas构造函数
    const canvasElement = document.createElement('canvas');
    const canvas = new Canvas(canvasElement);
    console.log('✅ Canvas创建成功:', canvas);
    
    // 测试Circle构造函数
    const circle = new Circle({ radius: 10, fill: 'red' });
    console.log('✅ Circle创建成功:', circle);
    
    // 测试Text构造函数
    const text = new Text('测试文本', { fontSize: 12 });
    console.log('✅ Text创建成功:', text);
    
    // 测试FabricImage静态方法
    console.log('✅ FabricImage.fromURL方法存在:', typeof FabricImage.fromURL === 'function');
    
    return true;
  } catch (error) {
    console.error('❌ Fabric.js测试失败:', error);
    return false;
  }
};
