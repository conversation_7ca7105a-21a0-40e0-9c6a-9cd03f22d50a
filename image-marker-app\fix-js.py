import os
import re

# 读取构建后的JavaScript文件
js_file_path = os.path.join('dist', 'assets', 'index-BCn2mr22.js')

try:
    print('🔧 开始修复Fabric.js API调用...')
    
    with open(js_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 替换API调用为Fabric.js v5兼容版本
    # 将 fabric.Image.fromURL 或 fabric.FabricImage.fromURL 替换为 fabric.Image.fromURL
    content = re.sub(r'fabric\.FabricImage\.fromURL', 'fabric.Image.fromURL', content)
    
    # 确保使用正确的API
    content = re.sub(r'fabric\.Image\.fromURL\s*\(\s*([^,]+)\s*,\s*\{[^}]*\}\s*\)', 
                     r'fabric.Image.fromURL(\1)', content)
    
    if content != original_content:
        with open(js_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print('✅ 成功修复Fabric.js API调用')
        print('📝 已修复API调用兼容性')
    else:
        print('ℹ️ 未找到需要修复的API调用')
        
except Exception as error:
    print(f'❌ 修复失败: {error}')
