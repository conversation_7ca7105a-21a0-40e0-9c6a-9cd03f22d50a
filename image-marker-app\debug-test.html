<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric.js 图片加载测试</title>
    <!-- Fabric.js v6.6.6 with fallback loading -->
    <script>
        // Primary CDN
        function loadFabricJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        console.log('Fabric.js loaded successfully from primary CDN');
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from primary CDN'));
                script.src = 'https://unpkg.com/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        // Fallback CDN
        function loadFabricJSFallback() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        console.log('Fabric.js loaded successfully from fallback CDN');
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from fallback CDN'));
                script.src = 'https://cdn.jsdelivr.net/npm/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        // Load with fallback
        loadFabricJS().catch(() => {
            console.warn('Primary CDN failed, trying fallback...');
            return loadFabricJSFallback();
        }).catch(() => {
            console.error('All CDN sources failed to load Fabric.js');
            document.body.innerHTML = '<div style="color: red; text-align: center; margin-top: 50px;"><h2>❌ Failed to load Fabric.js</h2><p>Please check your internet connection and try again.</p></div>';
        });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-title {
            color: #1890ff;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .canvas-container {
            border: 2px dashed #d9d9d9;
            padding: 20px;
            text-align: center;
            margin: 15px 0;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .log {
            background: #f6f6f6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fabric.js v6 图片加载调试测试</h1>
        
        <div class="test-section">
            <div class="test-title">📷 测试1: 基础Canvas初始化</div>
            <div class="canvas-container">
                <canvas id="test-canvas-1" width="400" height="300" style="border: 1px solid #ccc;"></canvas>
            </div>
            <button onclick="testCanvasInit()">初始化Canvas</button>
            <div id="log-1" class="log"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🖼️ 测试2: SVG图片加载</div>
            <div class="canvas-container">
                <canvas id="test-canvas-2" width="400" height="300" style="border: 1px solid #ccc;"></canvas>
            </div>
            <button onclick="testSVGLoad()">加载SVG示例图片</button>
            <div id="log-2" class="log"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📁 测试3: 文件上传图片加载</div>
            <div class="canvas-container">
                <canvas id="test-canvas-3" width="400" height="300" style="border: 1px solid #ccc;"></canvas>
            </div>
            <input type="file" id="file-input" accept="image/*" onchange="testFileLoad(this)">
            <div id="log-3" class="log"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 测试4: 标记点添加</div>
            <div class="canvas-container">
                <canvas id="test-canvas-4" width="400" height="300" style="border: 1px solid #ccc;"></canvas>
            </div>
            <button onclick="testMarkers()">添加测试标记点</button>
            <div id="log-4" class="log"></div>
        </div>
    </div>

    <script>
        let canvases = {};

        function log(testId, message) {
            const logElement = document.getElementById(`log-${testId}`);
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`Test ${testId}:`, message);
        }

        function testCanvasInit() {
            try {
                log(1, '开始初始化Canvas...');
                if (typeof fabric === 'undefined') {
                    log(1, '❌ Fabric.js 未加载');
                    return;
                }
                log(1, `Fabric.js 版本: ${fabric.version || 'unknown'}`);

                const canvasElement = document.getElementById('test-canvas-1');
                const canvas = new fabric.Canvas(canvasElement, {
                    selection: true,
                    backgroundColor: '#f0f8ff'
                });
                canvases['test1'] = canvas;
                log(1, '✅ Canvas初始化成功');
                log(1, `Canvas尺寸: ${canvas.width}x${canvas.height}`);
                log(1, `Canvas类型: ${canvas.constructor.name}`);
            } catch (error) {
                log(1, `❌ Canvas初始化失败: ${error.message}`);
                console.error('Canvas初始化错误:', error);
            }
        }

        function testSVGLoad() {
            try {
                log(2, '开始加载SVG图片...');
                if (typeof fabric === 'undefined') {
                    log(2, '❌ Fabric.js 未加载');
                    return;
                }

                const canvasElement = document.getElementById('test-canvas-2');
                const canvas = new fabric.Canvas(canvasElement);
                canvases['test2'] = canvas;

                const svgContent = `
                    <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="#e6f7ff"/>
                        <rect x="50" y="50" width="200" height="100" fill="#1890ff" rx="10"/>
                        <text x="150" y="110" text-anchor="middle" font-family="Arial" font-size="16" fill="white">
                            测试图片
                        </text>
                        <circle cx="100" cy="150" r="5" fill="#52c41a"/>
                        <circle cx="200" cy="150" r="5" fill="#ff4d4f"/>
                    </svg>
                `;

                const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);

                log(2, `SVG URL: ${url}`);
                log(2, `使用 fabric.FabricImage.fromURL 加载图片...`);

                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(2, `✅ 图片对象创建成功: ${img.width}x${img.height}`);
                    log(2, `图片类型: ${img.constructor.name}`);

                    canvas.clear();
                    canvas.setDimensions({ width: 400, height: 300 });

                    img.set({
                        left: 50,
                        top: 50,
                        selectable: false
                    });

                    canvas.add(img);
                    canvas.renderAll();

                    log(2, '✅ 图片添加到Canvas成功');
                    log(2, `Canvas对象数量: ${canvas.getObjects().length}`);

                    // 清理URL
                    URL.revokeObjectURL(url);
                }).catch((error) => {
                    log(2, `❌ 图片加载失败: ${error.message}`);
                    console.error('SVG加载错误:', error);
                    URL.revokeObjectURL(url);
                });
            } catch (error) {
                log(2, `❌ SVG测试失败: ${error.message}`);
                console.error('SVG测试错误:', error);
            }
        }

        function testFileLoad(input) {
            const file = input.files[0];
            if (!file) return;

            try {
                log(3, `开始加载文件: ${file.name} (${file.size} bytes)`);
                if (typeof fabric === 'undefined') {
                    log(3, '❌ Fabric.js 未加载');
                    return;
                }

                const canvasElement = document.getElementById('test-canvas-3');
                const canvas = new fabric.Canvas(canvasElement);
                canvases['test3'] = canvas;

                const url = URL.createObjectURL(file);
                log(3, `文件URL: ${url}`);
                log(3, `文件类型: ${file.type}`);

                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(3, `✅ 图片对象创建成功: ${img.width}x${img.height}`);
                    log(3, `图片类型: ${img.constructor.name}`);

                    canvas.clear();

                    // 计算缩放
                    const maxWidth = 350;
                    const maxHeight = 250;
                    const scaleX = maxWidth / img.width;
                    const scaleY = maxHeight / img.height;
                    const scale = Math.min(scaleX, scaleY, 1);

                    img.set({
                        left: 25,
                        top: 25,
                        scaleX: scale,
                        scaleY: scale,
                        selectable: false
                    });

                    canvas.add(img);
                    canvas.renderAll();

                    log(3, `✅ 图片添加成功，缩放比例: ${scale.toFixed(2)}`);
                    log(3, `Canvas对象数量: ${canvas.getObjects().length}`);

                    // 清理URL
                    URL.revokeObjectURL(url);
                }).catch((error) => {
                    log(3, `❌ 图片加载失败: ${error.message}`);
                    console.error('文件加载错误:', error);
                    URL.revokeObjectURL(url);
                });
            } catch (error) {
                log(3, `❌ 文件加载测试失败: ${error.message}`);
                console.error('文件测试错误:', error);
            }
        }

        function testMarkers() {
            try {
                log(4, '开始测试标记点...');
                if (typeof fabric === 'undefined') {
                    log(4, '❌ Fabric.js 未加载');
                    return;
                }

                const canvasElement = document.getElementById('test-canvas-4');
                const canvas = new fabric.Canvas(canvasElement, {
                    backgroundColor: '#f8f9fa'
                });
                canvases['test4'] = canvas;

                // 添加几个测试标记点
                const markers = [
                    { x: 100, y: 100, color: '#ff4d4f', name: '标记1' },
                    { x: 200, y: 150, color: '#52c41a', name: '标记2' },
                    { x: 300, y: 200, color: '#1890ff', name: '标记3' }
                ];

                markers.forEach((marker, index) => {
                    const circle = new fabric.Circle({
                        left: marker.x - 5,
                        top: marker.y - 5,
                        radius: 5,
                        fill: marker.color,
                        stroke: '#ffffff',
                        strokeWidth: 2,
                        selectable: true
                    });

                    const text = new fabric.Text(marker.name, {
                        left: marker.x + 8,
                        top: marker.y - 8,
                        fontSize: 12,
                        fill: marker.color,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)'
                    });

                    canvas.add(circle);
                    canvas.add(text);

                    log(4, `✅ 添加标记点: ${marker.name} at (${marker.x}, ${marker.y})`);
                });

                canvas.renderAll();
                log(4, `✅ 所有标记点添加完成，总数: ${canvas.getObjects().length}`);
                log(4, `Canvas类型: ${canvas.constructor.name}`);
                log(4, `Circle类型: ${fabric.Circle.name}`);
                log(4, `Text类型: ${fabric.Text.name}`);
            } catch (error) {
                log(4, `❌ 标记点测试失败: ${error.message}`);
                console.error('标记点测试错误:', error);
            }
        }

        // 页面加载完成后自动运行基础测试
        window.onload = function() {
            // 等待Fabric.js加载完成
            const checkFabric = () => {
                if (typeof fabric !== 'undefined') {
                    console.log('Fabric.js version:', fabric.version);
                    log(1, `Fabric.js 版本: ${fabric.version || 'unknown'}`);
                    log(1, '页面加载完成，可以开始测试');
                    log(1, `可用的Fabric类: ${Object.keys(fabric).filter(key => typeof fabric[key] === 'function').join(', ')}`);
                } else {
                    log(1, '⏳ 等待Fabric.js加载...');
                    setTimeout(checkFabric, 100);
                }
            };
            checkFabric();
        };
    </script>
</body>
</html>
