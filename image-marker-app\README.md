# 图片标记管理系统

一个基于React + TypeScript + Vite开发的现代化图片标记管理Web应用程序，支持在图片上添加标记点、分组管理、数据导入导出等功能。

## 🚀 功能特性

### 核心功能
- **图片上传**：支持拖拽上传，支持JPG、PNG、GIF、WebP格式
- **标记添加**：点击图片任意位置添加标记点
- **坐标记录**：精确记录标记点的X、Y坐标位置
- **实时预览**：所见即所得的标记编辑体验

### 标记管理
- **分组管理**：创建不同的标记组/类别，支持颜色区分
- **批量操作**：支持批量选择和删除多个标记
- **标记编辑**：修改标记名称、坐标、颜色、分组等属性
- **显示控制**：支持显示/隐藏特定标记或整个标记组
- **拖拽移动**：直接拖拽标记点调整位置

### 数据管理
- **Excel导出**：将标记数据导出为Excel格式文件
- **Excel导入**：从Excel文件批量导入标记数据
- **数据模板**：提供标准的Excel导入模板
- **自动分组**：导入时自动创建不存在的分组

### 界面特性
- **响应式设计**：支持桌面端、平板、手机等不同屏幕尺寸
- **中文界面**：完全中文化的用户界面
- **现代化UI**：基于Ant Design的美观界面
- **实时反馈**：操作结果的即时提示和反馈

## 🛠️ 技术栈

- **前端框架**：React 18 + TypeScript
- **构建工具**：Vite
- **UI组件库**：Ant Design
- **画布操作**：Fabric.js
- **Excel处理**：SheetJS
- **状态管理**：React Context + useReducer
- **样式方案**：CSS Modules + Ant Design

## 📦 安装和运行

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 http://localhost:5173 启动

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📖 使用指南

### 1. 上传图片
- 点击上传区域或拖拽图片文件到上传区域
- 支持的格式：JPG、PNG、GIF、WebP
- 文件大小限制：10MB

### 2. 添加标记
- 在左侧分组管理中选择要使用的分组
- 点击图片上的任意位置添加标记点
- 标记点会自动使用当前选中分组的颜色

### 3. 管理标记
- 在右侧标记管理面板中查看所有标记
- 点击标记可以选择，支持多选
- 使用编辑按钮修改标记属性
- 使用眼睛图标控制标记的显示/隐藏

### 4. 分组管理
- 在左侧分组管理中创建新分组
- 为分组设置名称、颜色和描述
- 切换分组的显示/隐藏状态
- 删除不需要的分组（标记会移动到默认分组）

### 5. 数据导入导出
- 点击"导出Excel"按钮将当前数据导出
- 点击"选择Excel文件导入"导入数据
- 下载导入模板了解数据格式要求

## 📊 数据格式

### Excel导出格式
| 列名 | 说明 | 示例 |
|------|------|------|
| 标记名称 | 标记的名称 | 标记1 |
| X坐标 | 标记的X坐标 | 100 |
| Y坐标 | 标记的Y坐标 | 150 |
| 分组名称 | 所属分组 | 默认分组 |
| 颜色 | 标记颜色 | #1890ff |
| 是否可见 | 显示状态 | 是/否 |
| 创建时间 | 创建时间 | 2024-01-01 12:00:00 |
| 更新时间 | 最后更新时间 | 2024-01-01 12:00:00 |

### Excel导入要求
- **必需列**：标记名称、X坐标、Y坐标
- **可选列**：分组名称、颜色、是否可见
- 如果分组不存在，系统会自动创建
- 坐标值必须为数字类型

## 🎯 应用场景

- **图像标注**：为机器学习数据集标注关键点
- **建筑测量**：在建筑图纸上标记测量点
- **医学影像**：在医学图像上标记病灶位置
- **地图标记**：在地图上标记兴趣点
- **产品检测**：在产品图片上标记缺陷位置
- **教学演示**：在教学图片上标记重点内容

## 🔧 开发说明

### 项目结构
```
src/
├── components/          # 组件目录
│   ├── ImageUploader/   # 图片上传组件
│   ├── ImageCanvas/     # 图片画布组件
│   ├── MarkerManager/   # 标记管理组件
│   ├── GroupManager/    # 分组管理组件
│   └── DataExporter/    # 数据导入导出组件
├── context/             # 状态管理
├── types/               # TypeScript类型定义
├── utils/               # 工具函数
└── styles/              # 样式文件
```

### 核心依赖
- `antd`: UI组件库
- `fabric`: 画布操作库
- `xlsx`: Excel文件处理
- `@types/fabric`: Fabric.js类型定义

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🎉 支持图片上传和标记添加
- 📊 支持Excel数据导入导出
- 🎨 响应式界面设计
- 🔧 完整的分组管理功能

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者邮箱
