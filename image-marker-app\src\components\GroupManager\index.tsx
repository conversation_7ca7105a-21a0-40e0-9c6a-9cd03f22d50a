import React, { useState } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Modal,
  Form,
  Input,
  ColorPicker,
  message,
  Typography,
  Tooltip,
  Popconfirm,
  Badge,
  Radio
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useApp } from '../../context/AppContext';
import type { MarkerGroup } from '../../types';
import './index.css';

const { Title } = Typography;
const { TextArea } = Input;

interface GroupModalProps {
  visible: boolean;
  group: MarkerGroup | null;
  onCancel: () => void;
  onOk: (values: any) => void;
  title: string;
}

const GroupModal: React.FC<GroupModalProps> = ({ visible, group, onCancel, onOk, title }) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (group) {
      form.setFieldsValue({
        name: group.name,
        color: group.color,
        description: group.description || ''
      });
    } else {
      form.resetFields();
    }
  }, [group, form, visible]);

  const handleOk = () => {
    form.validateFields().then(values => {
      onOk({
        ...values,
        color: typeof values.color === 'string' ? values.color : values.color.toHexString()
      });
      form.resetFields();
    });
  };

  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      okText="确定"
      cancelText="取消"
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="分组名称"
          rules={[
            { required: true, message: '请输入分组名称' },
            { max: 20, message: '分组名称不能超过20个字符' }
          ]}
        >
          <Input placeholder="请输入分组名称" />
        </Form.Item>
        
        <Form.Item
          name="color"
          label="分组颜色"
          rules={[{ required: true, message: '请选择颜色' }]}
        >
          <ColorPicker showText />
        </Form.Item>

        <Form.Item
          name="description"
          label="分组描述"
        >
          <TextArea
            placeholder="请输入分组描述（可选）"
            rows={3}
            maxLength={100}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

const GroupManager: React.FC = () => {
  const { state, addGroup, updateGroup, deleteGroup, toggleGroupVisibility, selectGroup } = useApp();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingGroup, setEditingGroup] = useState<MarkerGroup | null>(null);

  // 获取分组中的标记数量
  const getMarkerCount = (groupId: string) => {
    return state.markers.filter(marker => marker.groupId === groupId).length;
  };

  // 处理添加分组
  const handleAdd = () => {
    setEditingGroup(null);
    setModalVisible(true);
  };

  // 处理编辑分组
  const handleEdit = (group: MarkerGroup) => {
    setEditingGroup(group);
    setModalVisible(true);
  };

  // 处理模态框确认
  const handleModalOk = (values: any) => {
    if (editingGroup) {
      // 编辑模式
      updateGroup(editingGroup.id, values);
      message.success('分组更新成功');
    } else {
      // 添加模式
      const groupId = addGroup(values.name, values.description);
      updateGroup(groupId, { color: values.color });
      message.success('分组创建成功');
    }
    setModalVisible(false);
    setEditingGroup(null);
  };

  // 处理删除分组
  const handleDelete = (group: MarkerGroup) => {
    if (group.id === 'default') {
      message.error('不能删除默认分组');
      return;
    }
    
    const markerCount = getMarkerCount(group.id);
    if (markerCount > 0) {
      Modal.confirm({
        title: '确定要删除这个分组吗？',
        content: `该分组中有 ${markerCount} 个标记，删除后这些标记将移动到默认分组。`,
        okText: '确定删除',
        cancelText: '取消',
        onOk: () => {
          deleteGroup(group.id);
          message.success('分组删除成功');
        }
      });
    } else {
      deleteGroup(group.id);
      message.success('分组删除成功');
    }
  };

  // 处理可见性切换
  const handleToggleVisibility = (groupId: string) => {
    toggleGroupVisibility(groupId);
  };

  // 处理分组选择
  const handleSelectGroup = (groupId: string) => {
    selectGroup(groupId);
  };

  return (
    <Card className="group-manager">
      <div className="manager-header">
        <Title level={5} style={{ margin: 0 }}>
          分组管理 ({state.groups.length})
        </Title>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          新建分组
        </Button>
      </div>

      <div className="group-selection">
        <Typography.Text strong>当前选中分组：</Typography.Text>
        <Radio.Group
          value={state.selectedGroup}
          onChange={(e) => handleSelectGroup(e.target.value)}
          style={{ marginTop: 8 }}
        >
          <Space direction="vertical" size="small">
            {state.groups.map(group => (
              <Radio key={group.id} value={group.id}>
                <Space>
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      backgroundColor: group.color,
                      borderRadius: '50%'
                    }}
                  />
                  {group.name}
                  <Badge count={getMarkerCount(group.id)} size="small" />
                </Space>
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </div>

      <List
        className="group-list"
        dataSource={state.groups}
        renderItem={(group) => (
          <List.Item
            className={`group-item ${!group.visible ? 'group-hidden' : ''}`}
            actions={[
              <Tooltip title={group.visible ? '隐藏分组' : '显示分组'} key="visibility">
                <Button
                  type="text"
                  size="small"
                  icon={group.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                  onClick={() => handleToggleVisibility(group.id)}
                  style={{ color: group.visible ? '#52c41a' : '#999' }}
                />
              </Tooltip>,
              <Tooltip title="编辑分组" key="edit">
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(group)}
                />
              </Tooltip>,
              group.id !== 'default' && (
                <Popconfirm
                  title="确定要删除这个分组吗？"
                  description={getMarkerCount(group.id) > 0 ? 
                    `该分组中有 ${getMarkerCount(group.id)} 个标记，删除后将移动到默认分组` : 
                    undefined
                  }
                  onConfirm={() => handleDelete(group)}
                  okText="确定"
                  cancelText="取消"
                  key="delete"
                >
                  <Tooltip title="删除分组">
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
              )
            ].filter(Boolean)}
          >
            <List.Item.Meta
              avatar={
                <div
                  className="group-color-indicator"
                  style={{ backgroundColor: group.color }}
                />
              }
              title={
                <Space>
                  <span className="group-name">{group.name}</span>
                  <Badge count={getMarkerCount(group.id)} size="small" />
                  {group.id === state.selectedGroup && (
                    <span className="selected-indicator">当前选中</span>
                  )}
                </Space>
              }
              description={
                <div className="group-description">
                  {group.description && (
                    <div className="description-text">{group.description}</div>
                  )}
                  <div className="group-meta">
                    创建时间：{group.createdAt.toLocaleString('zh-CN')}
                  </div>
                </div>
              }
            />
          </List.Item>
        )}
      />

      <GroupModal
        visible={modalVisible}
        group={editingGroup}
        title={editingGroup ? '编辑分组' : '新建分组'}
        onCancel={() => {
          setModalVisible(false);
          setEditingGroup(null);
        }}
        onOk={handleModalOk}
      />
    </Card>
  );
};

export default GroupManager;
