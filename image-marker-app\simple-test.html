<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Fabric.js测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        canvas {
            border: 2px solid #d9d9d9;
            border-radius: 4px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .log {
            background: #f6f6f6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .warning { background: #fffbe6; border: 1px solid #ffe58f; color: #faad14; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Fabric.js v6 简单测试</h1>
        
        <div class="test-section">
            <h3>📦 Fabric.js 加载测试</h3>
            <div id="import-status" class="status warning">正在检查Fabric.js导入...</div>
            <button onclick="testImports()">测试导入</button>
        </div>

        <div class="test-section">
            <h3>🎨 Canvas 创建测试</h3>
            <canvas id="test-canvas" width="400" height="300"></canvas>
            <br>
            <button onclick="testCanvas()">创建Canvas</button>
            <button onclick="testImage()">加载示例图片</button>
            <button onclick="testMarkers()">添加标记点</button>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <!-- 使用与调试页面相同的CDN加载方式 -->
    <script>
        // Primary CDN
        function loadFabricJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        console.log('Fabric.js loaded successfully from primary CDN');
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from primary CDN'));
                script.src = 'https://unpkg.com/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        // Fallback CDN
        function loadFabricJSFallback() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof fabric !== 'undefined') {
                        console.log('Fabric.js loaded successfully from fallback CDN');
                        resolve();
                    } else {
                        reject(new Error('Fabric.js object not found'));
                    }
                };
                script.onerror = () => reject(new Error('Failed to load from fallback CDN'));
                script.src = 'https://cdn.jsdelivr.net/npm/fabric@6.6.6/dist/index.min.js';
                document.head.appendChild(script);
            });
        }

        let fabricCanvas = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, message, type = 'warning') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function testImports() {
            if (typeof fabric !== 'undefined') {
                log('✅ Fabric.js 已加载');
                log(`版本: ${fabric.version || 'unknown'}`);
                log(`Canvas: ${typeof fabric.Canvas}`);
                log(`FabricImage: ${typeof fabric.FabricImage}`);
                log(`Circle: ${typeof fabric.Circle}`);
                log(`Text: ${typeof fabric.Text}`);
                log(`FabricImage.fromURL: ${typeof fabric.FabricImage?.fromURL}`);
                updateStatus('import-status', '✅ Fabric.js 导入成功', 'success');
            } else {
                log('❌ Fabric.js 未加载');
                updateStatus('import-status', '❌ Fabric.js 导入失败', 'error');
            }
        }

        function testCanvas() {
            try {
                const canvasElement = document.getElementById('test-canvas');
                fabricCanvas = new fabric.Canvas(canvasElement, {
                    backgroundColor: '#f0f8ff'
                });
                log('✅ Canvas 创建成功');
                log(`Canvas 尺寸: ${fabricCanvas.width}x${fabricCanvas.height}`);
            } catch (error) {
                log(`❌ Canvas 创建失败: ${error.message}`);
            }
        }

        function testImage() {
            if (!fabricCanvas) {
                log('❌ 请先创建Canvas');
                return;
            }

            try {
                // 创建示例SVG
                const svgContent = `
                    <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="#e6f7ff"/>
                        <rect x="50" y="50" width="200" height="100" fill="#1890ff" rx="10"/>
                        <text x="150" y="110" text-anchor="middle" font-family="Arial" font-size="16" fill="white">
                            测试图片
                        </text>
                    </svg>
                `;

                const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);

                log('开始加载示例图片...');

                fabric.FabricImage.fromURL(url, {
                    crossOrigin: 'anonymous'
                }).then((img) => {
                    log(`✅ 图片加载成功: ${img.width}x${img.height}`);
                    
                    fabricCanvas.clear();
                    
                    img.set({
                        left: 50,
                        top: 50,
                        selectable: false
                    });
                    
                    fabricCanvas.add(img);
                    fabricCanvas.renderAll();
                    
                    log('✅ 图片添加到Canvas成功');
                    URL.revokeObjectURL(url);
                }).catch((error) => {
                    log(`❌ 图片加载失败: ${error.message}`);
                    URL.revokeObjectURL(url);
                });
            } catch (error) {
                log(`❌ 图片测试失败: ${error.message}`);
            }
        }

        function testMarkers() {
            if (!fabricCanvas) {
                log('❌ 请先创建Canvas');
                return;
            }

            try {
                const circle = new fabric.Circle({
                    left: 100,
                    top: 100,
                    radius: 5,
                    fill: '#ff4d4f',
                    stroke: '#ffffff',
                    strokeWidth: 2
                });

                const text = new fabric.Text('标记1', {
                    left: 110,
                    top: 95,
                    fontSize: 12,
                    fill: '#ff4d4f'
                });

                fabricCanvas.add(circle);
                fabricCanvas.add(text);
                fabricCanvas.renderAll();

                log('✅ 标记点添加成功');
            } catch (error) {
                log(`❌ 标记点添加失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动加载Fabric.js
        window.onload = function() {
            log('页面加载完成，开始加载Fabric.js...');
            
            loadFabricJS().catch(() => {
                log('主CDN失败，尝试备用CDN...');
                return loadFabricJSFallback();
            }).then(() => {
                log('✅ Fabric.js 加载完成');
                updateStatus('import-status', '✅ Fabric.js 加载成功', 'success');
                testImports();
            }).catch(() => {
                log('❌ 所有CDN都失败');
                updateStatus('import-status', '❌ Fabric.js 加载失败', 'error');
            });
        };
    </script>
</body>
</html>
